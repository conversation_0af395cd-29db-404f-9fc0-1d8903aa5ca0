# contentmanager

- [dev environment](.dev/ReadMe.md)
- [e2e tests](e2e/ReadMe.md)
- [azure pipelines](.azure-pipelines/ReadMe.md)
- [k8s kustomization](k8s/ReadMe.md)

To clean up test cache: `go clean -testcache`

For Windows (use `grep` for nix): 
- Powershell: `go test ./... | Where { $_ -inotmatch '^(\?|ok)' }`
- nix: `go test ./... | grep -vE '^(\?|ok)' || [ $? -eq 1 ]`

## [Full-text search](go/pkgs/search_v2/ReadMe.md)

## [Social authentication](go/pkgs/sauth/ReadMe.md)

## [Notifications](go/pkgs/notifications/ReadMe.md)

## [Parent portal](https://dev.azure.com/imagineeverything/Content%20Manager/_git/parent_portal): sample `jQuery` code for parents application home page widget

After Parent Portal is deployed, it will be available on `/api/v1/parents/*` path of CM.
- `[GET]  /sys/auth/whoami` -- returns current user info (it will work for non-authenticated users as well, they will have `PrivacyLevel = 0`)
- `[GET]  /sys/v1/parents/students` -- returns list of students associated with current user (requires authentication)
- `[POST] /sys/v1/parents/associate/:code` -- associates student with current user (requires authentication) by code (returns error if code is invalid or already used). Only guardians can associate students. So if code is valid but user's email is not `IsGuardian` in `contacts` list, it will return error.

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"
            integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
</head>
<body>
<div id="pp-parent-info"></div>
<div id="pp-students-info"></div>
<form id="pp-student-code">
    <input type="text" name="student_code" id="student_code" placeholder="XXXXX-XXXXX-XXXXX-XXXXX-XXXXX">
    <div class="error"></div>
    <input type="submit" value="Submit">
</form>

<script>
    $(document).ready(function(){
        function parentInfo() {
            const div = $("#pp-parent-info")
            div.html(`<pre>loading...</pre>`);
            $.ajax({
                url: "/sys/auth/whoami",
                type: "GET",
                xhrFields: {
                    withCredentials: true
                },
                success: function(data){
                    div.html(`<pre>${JSON.stringify(data, null, 4)}</pre>`);
                    students();
                },
                error: function(data){
                    console.log(data);
                }
            })
        }
        parentInfo();

        function students() {
            // TODO: provide links to assignments
            const div = $("#pp-students-info")
            div.html(`<pre>loading...</pre>`);
            $.ajax({
                url: "/sys/v1/parents/students",
                type: "GET",
                xhrFields: {
                    withCredentials: true
                },
                success: function(data){
                    div.html(`<pre>${JSON.stringify(data, null, 4)}</pre>`);
                },
                error: function(data){
                    console.log(data);
                }
            })
        }

        $("#pp-student-code").submit(function(e){
            const err = $("#pp-student-code .error");
            err.html("");
            e.preventDefault();
            var student_code = $("#student_code").val();
            $('#pp-student-code input[type="submit"]').attr('disabled', 'disabled');

            $.ajax({
                url: `/sys/v1/parents/associate/${student_code}`,
                type: "POST",
                xhrFields: {
                    withCredentials: true
                },
                success: function(data){
                    console.log(data);
                    students();
                },
                error: function(data){
                    console.log(data);
                    if (data['responseText']) err.html(data['responseText']);
                }
            }).always(function(){
                $('#pp-student-code input[type="submit"]').removeAttr('disabled');
            })
        })
    })
</script>
</body>
</html>
```
