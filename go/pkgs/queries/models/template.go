package models

import (
	"contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/auth"
	"github.com/satori/go.uuid"
)

type (
	Template struct {
		auth.TenantWideBase
		commonModels.Entity
		commonModels.Tracking
		commonModels.XData
		Active bool

		Title           string
		Type            string // table, cards, user
		QueryID         *uuid.UUID
		StructureID     *uuid.UUID
		Description     string
		CSS             string
		JS              string
		ItemTemplate    string
		GroupTemplate   string
		WrapperTemplate string
	}

	TableCell struct {
		Label  string
		Name   string
		Config map[string]interface{}
	}
)

func (t Template) TableName() string {
	return "templates"
}

func (t Template) GetScopeEntity() string {
	return "cm.query.template"
}
