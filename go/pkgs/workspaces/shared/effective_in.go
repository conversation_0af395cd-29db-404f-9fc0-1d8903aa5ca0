package shared

import "contentmanager/library/utils/slicexx"

func EffectiveIn(workspace string, tenantWorkspaces []string, entityWorkspaces []string) []string {
	if workspace != "live" {
		return []string{}
	}
	effectiveIn := []string{}

	for _, w := range tenantWorkspaces {
		if w == "live" {
			continue
		}
		if !slicexx.Contains(entityWorkspaces, w) {
			effectiveIn = append(effectiveIn, w)
		}
	}

	return effectiveIn
}
