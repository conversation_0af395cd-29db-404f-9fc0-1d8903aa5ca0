
export GO111MODULE        := on
export PWD                := $(pwd)
export BUILD_DATE         := $(date -u +"%Y-%m-%dT%H:%M:%SZ")
export VCS_REF            := $(git rev-parse HEAD)
export IE_GIT_PATH        := $(IE_GIT_PAT) # required for private repos to build golang apps inside Docker.
										   # Looks like: <username>:<token> (token is a personal access token)
										   # https://docs.github.com/en/github/authenticating-to-github/creating-a-personal-access-token
										   # You should store this as an environment variable (IE_GIT_PATH) in your shell
TAG ?= latest
K_TEST_CONTEXT ?= aws-qa-130-contentmanager
# arn:aws:eks:ca-central-1:793554545599:cluster/QaEksCluster130

go-test:
	cd ./go && go test -v ./...
public:
	docker build --build-arg IE_GIT_PAT=$(IE_GIT_PAT) --target cm-public -t cm-public:$(TAG) -f build/docker/Dockerfile-golang .
api:
	docker build --build-arg IE_GIT_PAT=$(IE_GIT_PAT) --target cm-api -t cm-api:$(TAG) -f build/docker/Dockerfile-golang .
ui:
	docker build --target cm-admin-ui -t cm-admin-ui:$(TAG) -f build/docker/Dockerfile-react .

test-deploy:
	@if [ -z "$(hash)" ]; then echo "Error: hash is not set. Usage: make test-deploy hash=<hash>"; exit 1; fi
	@echo "Deploying with version $(hash)"
	@sed 's/\$${VERSION}/$(hash)/g' build/kubernetes/test/contentmanager_api_deployment.yaml | kubectl --context=$(K_TEST_CONTEXT) apply -f -
	@sed 's/\$${VERSION}/$(hash)/g' build/kubernetes/test/contentmanager_app_deployment.yaml | kubectl --context=$(K_TEST_CONTEXT) apply -f -
	@sed 's/\$${VERSION}/$(hash)/g' build/kubernetes/test/contentmanager_media_deployment.yaml | kubectl --context=$(K_TEST_CONTEXT) apply -f -
	@sed 's/\$${VERSION}/$(hash)/g' build/kubernetes/test/contentmanager_public_deployment.yaml | kubectl --context=$(K_TEST_CONTEXT) apply -f -
	@sed 's/\$${VERSION}/$(hash)/g' build/kubernetes/test/contentmanager_app_service.yaml | kubectl --context=$(K_TEST_CONTEXT) apply -f -
