services:
  dev-environment:
    restart: unless-stopped
    environment:
      PGHOST: postgres
      PGUSER: ${PGUSER}
      PGPASSWORD: ${PGPASSWORD}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION}
      AWS_DEFAULT_OUTPUT: ${AWS_DEFAULT_OUTPUT}
      TZ: America/Vancouver
    build:
      context: .
      dockerfile: .dev/Dockerfile-dev
    volumes:
      - .:/web/contentmanager
      - bash_history:/root
    depends_on:
      - postgres

  postgres:
    restart: unless-stopped
    image: postgres:15.9-alpine
    environment:
      POSTGRES_USER: ${PGUSER}
      POSTGRES_PASSWORD: ${PGPASSWORD}
      TZ: America/Vancouver
    ports:
      - "127.0.0.1:5432:5432"
    volumes:
      - postgres-data-15:/var/lib/postgresql/data

  caddy:
    image: caddy:2.7.6-alpine
    restart: unless-stopped
    ports:
      - "127.0.0.1:80:80"
      - "127.0.0.1:2019:2019"
      - "127.0.0.1:443:443"
      - "127.0.0.1:443:443/udp"
    volumes:
      - ./.dev/Caddyfile:/etc/caddy/Caddyfile
      - caddy_data:/data
      - caddy_config:/config

volumes:
  postgres-data:
  postgres-data-15:
  caddy_data:
  caddy_config:
  bash_history:
