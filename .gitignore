# IDE
.idea/
e2e/.auth/*

# ENV
/build/dependencies/.env
/*/*/*/.env
/*/*/*/*/.env
**/.env
/build/dependencies/*/*
/build/lambda/bootstrap
/build/lambda/contentmanager-lambda.zip
/lambda/bootstrap
/lambda/contentmanager-lambda.zip
# Local .terraform directories
**/.terraform/*

# .tfstate files
**/*.tfstate
**/*.tfstate.*

# Crash log files
**/crash.log
**/crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data, such as
# password, private keys, and other secrets. These should not be part of version
# control as they are data points which are potentially sensitive and subject
# to change depending on the environment.
**/*.tfvars
**/*.tfvars.json

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
**/override.tf
**/override.tf.json
**/*_override.tf
**/*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*

# Ignore CLI configuration files
**/.terraformrc
**/terraform.rc

# REACTJS
/react/build/
/react/node_modules/

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

/go/servers/experiments/**
.aider*
.claude*
CLAUDE*
.qodo*

.DS_Store

.qodo
~$*
