export PGHOST=localhost
export PGPORT=5432
export PGUSER=postgres

dblist=`psql -U postgres -d postgres -c "copy (select datname from pg_stat_database) to stdout"`
for db in $dblist ; do
    #skip the system databases
    if [[ $db == template0 ]] ||  [[ $db == template1 ]] || [[ $db == postgres ]] || [[ $db == gpdb ]] ; then
        continue
    fi
    echo processing $db
    #vacuum all tables
    psql -U postgres -d $db -e -a -c "VACUUM;"
    psql -U postgres -d $db -e -a -c "ANALYZE;"
done