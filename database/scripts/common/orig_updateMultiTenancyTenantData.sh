#!/bin/sh

DATABASE_IP=$1
DATABASE_PORT=$2
tenant_name=$3
tenant_description=$4
primary_domain=$5
db_name=$6
db_ip=$7
db_port=$8
db_user=$9
db_passwd="${10}"

echo "------------------------------------"
echo "EXEC updateMultiTenancytenantData.sh"
echo "------------------------------------"
echo 'Database IP:'$DATABASE_IP
echo 'Database Port:'$DATABASE_PORT
echo 'tenant Name:'$tenant_name
echo 'tenant Description:'$tenant_description
echo 'Primary Domain:'$primary_domain
echo 'DB Name:'$db_name
echo 'DB IP:'$db_ip
echo 'DB Port:'$db_port
echo 'DB User:'$db_user
echo 'DB Passwd:'$db_passwd

if [ $# = 10 ]
then
    echo Testing For tenant Record In MultiTenant, Creating If It Doesn''t Exists
    PGPASSWORD="$db_passwd" psql --host $DATABASE_IP --port $DATABASE_PORT --username "$db_user" -d "cm_multitenancy" -c "
        DO \$\$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM tenant WHERE name = '$tenant_name') THEN
                INSERT INTO tenant (name, description, host, server, dbuser, dbpassword)
                VALUES
                ('$tenant_name', '$tenant_description', '$db_ip', '$db_name', '$db_user', '$db_passwd');
            END IF;
        END\$\$
    ;"
    echo Testing For tenant Site Record In MultiTenant, Creating If It Doesn''t Exists
    PGPASSWORD="$db_passwd" psql --host $DATABASE_IP --port $DATABASE_PORT --username "$db_user" -d "cm_multitenancy" -c "
        DO \$\$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM site INNER JOIN tenant on site.tenant_id = tenant.id WHERE tenant.name like '%$tenant_name%') THEN
                INSERT INTO site (tenant_id, name, type, active)
                SELECT id, '$tenant_name Board Office Site', 'central office',true FROM tenant where name = '$tenant_name' limit 1;

                INSERT INTO Site (tenant_id, name, type, active)
                SELECT id, '$tenant_name High School Site', 'high school',true FROM tenant where name = '$tenant_name' limit 1;
            END IF;
        END\$\$
    ;"
    echo Testing For tenant Domain Record In Master, Creating If It Doesn''t Exists
    PGPASSWORD="$db_passwd" psql --host $DATABASE_IP --port $DATABASE_PORT --username "$db_user" -d "cm_multitenancy" -c "
        DO \$\$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM domain INNER JOIN site on domain.site_id = site.id and domain.tenant_id = site.tenant_id INNER JOIN tenant on site.tenant_id = tenant.id WHERE tenant.name like '%$tenant_name%') THEN
                INSERT INTO domain (tenant_id, site_id, domain, is_primary, is_site_default)
                SELECT site.tenant_id, site.id, '$primary_domain', false, true FROM site where name like '%$tenant_name Board Office Site%' limit 1;

                INSERT INTO domain (tenant_id, site_id, domain, is_primary, is_site_default)
                SELECT site.tenant_id, site.id, right('$db_name', length('$db_name') - 3) || '.imagineeverything.ca' , true, false FROM site where name like '%$tenant_name High School Site%' limit 1;
            END IF;
        END\$\$
    ;"
else
    echo "------------------------------------"
    echo "Syntax Error: Please run with updateMultiTenancytenantData.sh DATABASE_IP DATABASE_PORT tenant_name tenant_description primary_domain db_name db_ip db_port db_user db_passwd"
    echo "------------------------------------"
fi
