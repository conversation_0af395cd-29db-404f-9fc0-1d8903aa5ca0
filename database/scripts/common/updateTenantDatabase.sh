#!/bin/sh

tenant_deploy_planfile="/web/contentmanager/database/src/tenant/Deploy"
tenant_rebase_planfile="/web/contentmanager/database/src/tenant/Rebase"

db_name=$1
db_ip=$2
db_port=$3
db_user=$4
db_passwd=$5
db_action=$6

echo "----------------------------"
echo "EXEC updateTenantDatabase.sh"
echo "----------------------------"
echo 'DB Name:'$db_name
echo 'DB IP:'$db_ip
echo 'DB Port:'$db_port
echo 'DB User:'$db_user
echo 'DB Passwd:'$db_passwd
echo 'DB Action:'$db_action

if [ $# = 6 ]
then
    if [ "$db_action" = "recreate" ] 
    then
        PGPASSWORD="$db_passwd" psql --host $db_ip --port $db_port --username "$db_user" -d "postgres" -c "DROP DATABASE IF EXISTS $db_name;"
    fi

    PGPASSWORD="$db_passwd" psql --host $db_ip --port $db_port --username "$db_user" -d "postgres" -tc "SELECT 1 FROM pg_database WHERE datname = '$db_name';" | grep -q 1 || PGPASSWORD="$db_passwd" psql --host $db_ip --port $db_port --username "$db_user" -d "postgres" -c "CREATE DATABASE $db_name WITH ENCODING='UTF8' OWNER=postgres CONNECTION LIMIT=-1;"
       
    if [ "$db_action" = "revert_rebase" ]
    then
        sqitch --chdir "$tenant_rebase_planfile" -v  revert -y --target "db:pg://$db_user:$db_passwd@$db_ip/$db_name"
    fi

    if [ "$db_action" = "revert_all" ] 
    then
        sqitch --chdir "$tenant_rebase_planfile" -v  revert -y --target "db:pg://$db_user:$db_passwd@$db_ip/$db_name"
        sqitch --chdir "$tenant_deploy_planfile" -v  revert -y --target "db:pg://$db_user:$db_passwd@$db_ip/$db_name"
    fi

    if [ "$db_action" = "revert_last" ]
    then
        echo "--------- revert_last: $db_name ----------"
        sqitch --chdir "$tenant_deploy_planfile" -v  revert -y --target "db:pg://$db_user:$db_passwd@$db_ip/$db_name" @HEAD^1
    else
        sqitch -v  --chdir "$tenant_deploy_planfile" deploy --verify --target "db:pg://$db_user:$db_passwd@$db_ip/$db_name"
        sqitch -v  --chdir "$tenant_rebase_planfile" revert --log-only -y --target "db:pg://$db_user:$db_passwd@$db_ip/$db_name"
        sqitch -v  --chdir "$tenant_rebase_planfile" deploy --verify --target "db:pg://$db_user:$db_passwd@$db_ip/$db_name"
    fi

else
    echo "----------------------------"
    echo "Syntax Error: Please run with updateTenantDatabase.sh db_name db_ip db_port db_user db_passwd deploy_only/revert_rebase/revert_all/recreate"
    echo "----------------------------"
fi
