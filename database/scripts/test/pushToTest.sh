#!/bin/bash

SECTION_MULTITENANCY=0
DATABASE_IP='************'
DATABASE_PORT='5432'

if [ $# -eq 1 ]
then
    continue
else
    echo "Syntax Error: Please run with pushToTest.sh testDatabases.conf"
    exit
fi

while read line
do
    if [ "$line" = "[MULTITENANCY]" ]
    then
        SECTION_MULTITENANCY=1
        SECTION_TENANT=0
        continue
    fi

    if [ "$line" = "[TENANT]" ]
    then
        SECTION_MULTITENANCY=0
        SECTION_TENANT=1
        continue
    fi

    if [ $SECTION_MULTITENANCY == 1 ]
    then
        if [ ! -z "$line" ]
        then
            echo "----------------------------"
            echo "EXEC NEW MULTITENANCY SCRIPT"
            echo "----------------------------"
            db_master_name=$(echo "$line" | cut -f 1)
            db_master_user=$(echo "$line" | cut -f 2)
            db_master_passwd=$(echo "$line" | cut -f 3)
            echo 'DB Name:'$db_master_name
            echo 'DB IP:'$DATABASE_IP
            echo 'DB Port:'$DATABASE_PORT
            echo 'DB User:'$db_master_user
            echo 'DB Passwd:'$db_master_passwd
            /web/contentmanager/database/scripts/common/updateMultiTenancyDatabase.sh $db_master_name $DATABASE_IP $DATABASE_PORT $db_master_user $db_master_passwd deploy_only
            for job in `jobs -p`
            do
                echo $job
                wait $job
            done
        fi
    fi

    if [ $SECTION_TENANT == 1 ]
    then
      if [ ! -z "$line" ]
      then
        echo "--------------------"
        echo "EXEC NEW SITE SCRIPT"
        echo "--------------------"
        tenant_name=$(echo "$line" | cut -f 1)
        tenant_description=$(echo "$line" | cut -f 2)
        primary_domain=$(echo "$line" | cut -f 3)
        db_ip=$(echo "$line" | cut -f 4)
        db_port=$(echo "$line" | cut -f 5)
        db_user=$(echo "$line" | cut -f 6)
        db_passwd=$(echo "$line" | cut -f 7)
        db_name="${tenant_name// /}"
        db_name="cm_$(echo $db_name | tr '[A-Z]' '[a-z]')"
        echo 'DATABASE IP:'$DATABASE_IP
        echo 'DATABASE Port:'$DATABASE_PORT
        echo 'DB Name:'$db_name
        echo 'DB Description:'$tenant_description
        echo 'DB Primary Domain:'$primary_domain
        echo 'DB IP:'$db_ip
        echo 'DB Port:'$db_port
        echo 'DB User:'$db_user
        echo 'DB Passwd:'$db_passwd
#        /web/contentmanager/database/scripts/common/updateMultiTenancyTenantData.sh $DATABASE_IP $DATABASE_PORT "$tenant_name" "$tenant_description" "$primary_domain" "$db_name" "$db_ip" "$db_port" "$db_user" "$db_passwd"
        /web/contentmanager/database/scripts/common/updateTenantDatabase.sh $db_name $DATABASE_IP $DATABASE_PORT $db_master_user $db_master_passwd deploy_only
        for job in `jobs -p`
        do
          echo $job
          wait $job
        done
      fi
    fi

done < "$1"
exit 0;
