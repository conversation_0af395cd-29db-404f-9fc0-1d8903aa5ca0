-- Deploy [% change %]
[% FOREACH item IN requires -%]
-- requires: [% item %]
[% END -%]
[% FOREACH item IN conflicts -%]
-- conflicts: [% item %]
[% END -%]

BEGIN;

-- TABLE [% change %]

CREATE TABLE "[% change %]"
(
    "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
    CONSTRAINT pk_[% change %] PRIMARY KEY(id)
)
WITH (
    OIDS=FALSE
);

GRANT ALL ON TABLE ""[% change %]" TO contentmanager_application_user;

COMMIT;

