-- Verify cm_tenant_db_DeploySchema:2025-03-27-content_add_session_timestamp on pg

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'content' and column_name = 'extended_lock') THEN
        RAISE EXCEPTION 'extended_lock was not found on content table';
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'content' and column_name = 'current_editor') THEN
        RAISE EXCEPTION 'current_editor was not found on content table';
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'content' and column_name = 'editing_session') THEN
        RAISE EXCEPTION 'editing_session was not found on content table';
    END IF;

END$$;

