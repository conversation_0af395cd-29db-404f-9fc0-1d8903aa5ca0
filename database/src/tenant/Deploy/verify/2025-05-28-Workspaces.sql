-- Verify cm_tenant_db_DeploySchema:2025-05-20-Workspaces on pg

BEGIN;

DO $$
    BEGIN

        IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'workspaces')
        THEN RAISE EXCEPTION 'workspaces Table Does Not Exist In Deploy DB';
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'content' and column_name = 'workspace') THEN
            RAISE EXCEPTION 'workspace was not created in content table';
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'content' and column_name = 'effective_in') THEN
            RAISE EXCEPTION 'effective_in was not created in content table';
        END IF;

    END $$;

ROLLBACK;
