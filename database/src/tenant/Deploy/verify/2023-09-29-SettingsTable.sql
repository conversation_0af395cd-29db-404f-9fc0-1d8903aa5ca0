DO $$
    BEGIN

        IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'settings')
            THEN RAISE EXCEPTION 'settings Table Does Not Exist In Deploy DB';
        END IF;

        if NOT EXISTS (select 1 from pg_class where relname = 'idx_settings_name')
            THEN RAISE EXCEPTION 'idx_settings_name Index Does Not Exist In DB';
        END IF;

        if NOT EXISTS (select 1 from pg_class where relname = 'idx_settings_type')
            THEN RAISE EXCEPTION 'idx_settings_type Index Does Not Exist In DB';
        END IF;

        if NOT EXISTS (select 1 from pg_class where relname = 'idx_settings_data')
            THEN RAISE EXCEPTION 'idx_settings_data Index Does Not Exist In DB';
        END IF;

        if NOT EXISTS (select 1 from pg_class where relname = 'idx_settings_search')
            THEN RAISE EXCEPTION 'idx_settings_search Index Does Not Exist In DB';
        END IF;

    END$$;

