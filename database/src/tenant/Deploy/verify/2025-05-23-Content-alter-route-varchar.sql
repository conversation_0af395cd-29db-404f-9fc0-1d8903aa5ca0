-- Verify cm_tenant_db_DeploySchema:2025-05-16-Content-alter-route-varchar on pg

DO $$
    BEGIN

        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'content'
              AND column_name = 'route'
              AND data_type = 'character varying'
              AND character_maximum_length = 512
        ) THEN
            RAISE EXCEPTION 'content.route was not updated to varchar(512)';
        END IF;
END $$;
