DO $$
    BEGIN

        IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'pa_tokens')
        THEN RAISE EXCEPTION 'pa_tokens Table Does Not Exist In Deploy DB';
        END IF;

        if NOT EXISTS (select 1 from pg_constraint where confrelid = 'account'::regclass AND
                conname = 'fk_pa_tokens_account_id')
        THEN RAISE EXCEPTION 'fk_pa_tokens_account_id Constraint Does Not Exist In DB';
        END IF;

        if NOT EXISTS (select 1 from pg_class where relname = 'idx_pa_tokens_name')
        THEN RAISE EXCEPTION 'idx_pa_tokens_name Index Does Not Exist In DB';
        END IF;

        if NOT EXISTS (select 1 from pg_class where relname = 'idx_pa_tokens_account_name_active')
        THEN RAISE EXCEPTION 'idx_pa_tokens_account_name_active Index Does Not Exist In DB';
        END IF;

    END$$;
