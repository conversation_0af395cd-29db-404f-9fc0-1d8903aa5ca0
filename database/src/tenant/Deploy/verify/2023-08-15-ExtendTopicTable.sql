DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'topic' and column_name = 'title') THEN
            RAISE EXCEPTION 'title was not created in topic table';
        END IF;
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'topic' and column_name = 'description') THEN
            RAISE EXCEPTION 'title was not created in topic table';
        END IF;
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'topic' and column_name = 'topic_type' and data_type = 'character varying') THEN
            RAISE EXCEPTION 'topic_type was not modified in topic table';
        END IF;
    END$$;
