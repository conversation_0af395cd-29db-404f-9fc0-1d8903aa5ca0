-- Verify cm_tenant_db_DeploySchema:ContentTable_StartDate_EndDate on pg

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'content' and column_name = 'startdate') THEN
        RAISE EXCEPTION 'startdate missing from content table';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'content' and column_name = 'enddate') THEN
        RAISE EXCEPTION 'enddate missing from content table';
    END IF;
END$$;
