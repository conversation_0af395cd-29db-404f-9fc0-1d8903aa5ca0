-- Verify cm_tenant_db_DeploySchema:2025-05-05-Settings_for_search on pg

DO $$
    BEGIN

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'search_data' and column_name = 'settings') THEN
            RAISE EXCEPTION 'settings was not created in search_data table';
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'search_data' and column_name = 'meta') THEN
            RAISE EXCEPTION 'meta was not created in search_data table';
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'search_data' and column_name = 'media_id') THEN
            RAISE EXCEPTION 'media_id was not created in search_data table';
        END IF;

    END $$;
