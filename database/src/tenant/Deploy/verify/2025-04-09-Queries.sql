BEGIN;

DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'queries') THEN
            RAISE EXCEPTION 'Table queries does not exist';
        END IF;
        IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'templates') THEN
            RAISE EXCEPTION 'Table queries does not exist';
        END IF;
    END $$;

ROLLBACK;
