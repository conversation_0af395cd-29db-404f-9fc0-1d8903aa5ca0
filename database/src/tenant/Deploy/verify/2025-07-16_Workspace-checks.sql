-- Verify cm_tenant_db_DeploySchema:2025-07-16_Workspace-checks on pg

-- Verify no rows with empty workspace remain
DO $$
    DECLARE
        empty_count integer;
    BEGIN
        SELECT count(*) INTO empty_count
        FROM content
        WHERE workspace = '';

        IF empty_count > 0 THEN
            RAISE EXCEPTION 'Verification failed: % rows still have empty workspace', empty_count;
        END IF;
    END $$;

-- Verify the foreign key constraint exists
DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.table_constraints
            WHERE table_name = 'content'
              AND constraint_type = 'FOREIGN KEY'
              AND constraint_name = 'fk_workspace'
        ) THEN
            RAISE EXCEPTION 'Verification failed: foreign key constraint "fk_workspace" does not exist';
        END IF;
    END $$;

-- Verify the check constraint exists
DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.check_constraints
            WHERE constraint_name = 'check_non_live_must_be_active'
        ) THEN
            RAISE EXCEPTION 'Verification failed: check constraint "check_non_live_must_be_active" does not exist';
        END IF;
    END $$;
