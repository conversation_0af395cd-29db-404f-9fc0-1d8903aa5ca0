-- Deploy cm_tenant_db_DeploySchema:FullTextSearchIndex to pg

DO $$

    BEGIN
	if NOT EXISTS (select 1 from pg_class where relname = 'ix_content_tsvector_index_col')
		THEN RAISE EXCEPTION 'ix_content_tsvector_index_col Index Does Not Exist In DB';
    END IF;
	
	if NOT EXISTS (select 1 from pg_class where relname = 'ix_document_tsvector_index_col')
		THEN RAISE EXCEPTION 'ix_document_tsvector_index_col Index Does Not Exist In DB';
    END IF;

end$$;
