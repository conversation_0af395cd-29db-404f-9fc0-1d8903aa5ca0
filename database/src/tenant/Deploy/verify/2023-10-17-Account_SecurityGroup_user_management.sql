DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'account' and column_name = 'external_groups') THEN
            RAISE EXCEPTION 'external_groups was not created in account table';
        END IF;
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'account' and column_name = 'manual_groups') THEN
            RAISE EXCEPTION 'manual_groups was not created in account table';
        END IF;
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'account' and column_name = 'native_groups') THEN
            RAISE EXCEPTION 'native_groups was not created in account table';
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'security_group' and column_name = 'type') THEN
            RAISE EXCEPTION 'type was not created in security_group table';
        END IF;
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'security_group' and column_name = 'depends_on') THEN
            RAISE EXCEPTION 'depends_on was not created in security_group table';
        END IF;
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'security_group' and column_name = 'audience') THEN
            RAISE EXCEPTION 'audience was not created in security_group table';
        END IF;
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'security_group' and column_name = 'available_groups') THEN
            RAISE EXCEPTION 'available_groups was not created in security_group table';
        END IF;
    END$$;
