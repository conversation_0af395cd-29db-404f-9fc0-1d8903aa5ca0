-- Verify cm_tenant_db_DeploySchema:2025-03-13-Image-Renditions on pg
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'image_crop_size') THEN
        RAISE EXCEPTION 'image_crop_size table is missing';
END IF;

    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'media'
        AND (column_name = 'image_crop_size_ids' OR column_name = 'dirty_image_crop_size_ids')
    ) THEN
        RAISE EXCEPTION 'media table is missing columns: image_crop_size_ids or dirty_image_crop_size_ids';
END IF;
END $$;