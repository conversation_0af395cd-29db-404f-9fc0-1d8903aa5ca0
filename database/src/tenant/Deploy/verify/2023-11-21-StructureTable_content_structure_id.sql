-- Verify cm_tenant_db_DeploySchema:2023-11-21-StructureTable_content_structure_id on pg

DO $$
    BEGIN

        IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'structure')
        THEN RAISE EXCEPTION 'structure Table Does Not Exist In Deploy DB';
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'content' and column_name = 'structure_id') THEN
                RAISE EXCEPTION 'structure_id was not created in content table';
            END IF;

    END$$;
