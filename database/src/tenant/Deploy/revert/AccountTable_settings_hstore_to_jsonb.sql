-- Revert cm_tenant_db_DeploySchema:AccountTable_settings_hstore_to_jsonb from pg

BEGIN;
    CREATE OR REPLACE FUNCTION simple_jsonb_to_hstore(jdata jsonb)
    RETURNS hstore LANGUAGE SQL immutable
    AS $$
        SELECT hstore(array_agg(key), array_agg(value))
        FROM jsonb_each_text(jdata)
    $$;
    ALTER TABLE account ALTER COLUMN settings DROP DEFAULT;
    ALTER TABLE account ALTER COLUMN settings type hstore using simple_jsonb_to_hstore(account.settings);
    ALTER TABLE account ALTER COLUMN settings SET DEFAULT '';
    DROP FUNCTION IF EXISTS simple_jsonb_to_hstore;

COMMIT;
