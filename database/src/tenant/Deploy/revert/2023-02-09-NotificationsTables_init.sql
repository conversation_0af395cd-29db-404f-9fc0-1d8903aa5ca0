-- Deploy cm_tenant_db_DeploySchema:2023-02-09-NotificationsTables_init to pg

BEGIN;
ALTER TABLE ONLY bus_status DROP COLUMN IF EXISTS has_email_notification;
DROP TABLE IF EXISTS subscriber
    , topic
    , subscription
    , issue
    , schedule
    , email_config
    , audit_record;
DROP TYPE IF EXISTS
    topic_type
    , creation_type
    , delivery_type
    , relay_type
    , audit_event
    , issue_status;
COMMIT;