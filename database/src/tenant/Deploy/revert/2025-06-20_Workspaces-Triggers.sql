-- Revert cm_tenant_db_DeploySchema:2025-06-20_Workspaces-Triggers from pg

BEGIN;

----------------
-- Drop content-related components
----------------

-- Drop the content index
DROP INDEX IF EXISTS idx_content_id_workspace;

-- Revoke execute permission on content function
REVOKE EXECUTE ON FUNCTION public.content_update_effective_in() FROM contentmanager_application_user;

-- Drop the content trigger
DROP TRIGGER IF EXISTS tr_content_update_effective_in
    ON public.content;

-- Drop the content trigger function
DROP FUNCTION IF EXISTS public.content_update_effective_in();

----------------
-- Drop workspace materialized view components
----------------

-- Revoke execute permission on workspace function
REVOKE EXECUTE ON FUNCTION public.refresh_mv_active_workspaces() FROM contentmanager_application_user;

-- Drop the workspace trigger
DROP TRIGGER IF EXISTS trg_refresh_mv_active_workspaces
    ON public.workspaces;

-- Drop the workspace trigger function
DROP FUNCTION IF EXISTS public.refresh_mv_active_workspaces();

-- Drop the materialized view (this will also drop its unique index)
DROP MATERIALIZED VIEW IF EXISTS mv_active_workspaces;

ALTER TABLE public.content
    DROP CONSTRAINT IF EXISTS chk_non_live_must_be_active;

COMMIT;
