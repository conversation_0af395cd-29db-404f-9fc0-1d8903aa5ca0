-- Revert cm_tenant_db_DeploySchema:2025-05-16-Content-alter-route-varchar from pg

BEGIN;
    ALTER TABLE content DISABLE TRIGGER ALL;

    DROP INDEX IF EXISTS ix_content_tsvector_index_col;

    ALTER TABLE content DROP COLUMN IF EXISTS tsvector_index_col;

    ALTER TABLE content ALTER COLUMN route TYPE varchar(256);

    ALTER TABLE content
        ADD COLUMN if not exists tsvector_index_col tsvector
            GENERATED ALWAYS AS (
                setweight(to_tsvector('english', coalesce(title, '')), 'A')
                    || setweight(to_tsvector('english', coalesce(regexp_replace(replace(regexp_replace(content.content, '<[^>]*>', ' ', 'g'), '&nbsp;', ' '), '[^\s:/_-]{30,}', ' ', 'g'), '')), 'C')
                    || setweight(to_tsvector('english', coalesce(regexp_replace(content.data::text, '[^\s:/_-]{30,}', ' ', 'g'), '')), 'C')
                    || setweight(to_tsvector('english', coalesce(regexp_replace(content.route, '[^\s:/_-]{30,}', ' ', 'g'), '')), 'D')
                )
                STORED;

    CREATE INDEX if not exists ix_content_tsvector_index_col ON content USING GIN (tsvector_index_col);

-- Content History - Indexes are not required.
    ALTER TABLE content_history ALTER COLUMN route TYPE varchar(256);


    ALTER TABLE content ENABLE TRIGGER ALL;

COMMIT;
