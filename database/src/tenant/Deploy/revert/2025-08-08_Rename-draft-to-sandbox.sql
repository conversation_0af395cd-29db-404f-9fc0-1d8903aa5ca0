-- Revert cm_tenant_db_DeploySchema:2025-08-08_Rename-draft-to-sandbox to pg

BEGIN;

    ALTER TABLE content DISABLE TRIGGER ALL;

    INSERT INTO workspaces (id, description, active, created_at, created_by, updated_at, updated_by)
        VALUES ('draft', 'Draft content', true, now(), '45f06f48-a93c-414e-b9a0-7582e0abc085', now(), '45f06f48-a93c-414e-b9a0-7582e0abc085');

    -- Process `workspace`
    UPDATE content
        SET workspace = 'draft'
        WHERE workspace = 'sandbox';

    UPDATE content_history
        SET workspace = 'draft'
        WHERE workspace = 'sandbox';

    -- Process `effective_in`
    UPDATE content
        SET effective_in = array_replace(effective_in,
                                     'sandbox'::varchar,
                                     'draft'::varchar)
        WHERE 'sandbox' = ANY (effective_in);

    UPDATE content_history
        SET effective_in = array_replace(effective_in,
                                     'sandbox'::varchar,
                                     'draft'::varchar)
        WHERE 'sandbox' = ANY (effective_in);


    DELETE FROM workspaces WHERE id = 'sandbox';

    ALTER TABLE content ENABLE TRIGGER ALL;

COMMIT;
