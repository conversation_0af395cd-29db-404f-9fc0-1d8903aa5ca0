-- Deploy cm_tenant_db_RebaseSchema:content_update_navigation to pg

BEGIN;
    DROP TRIGGER IF EXISTS content_delete_trigger on content;
    DROP FUNCTION IF EXISTS content_delete();

    CREATE OR REPLACE FUNCTION content_delete()
        <PERSON><PERSON><PERSON><PERSON> trigger AS $$
        BEGIN
            IF OLD.active = true AND NEW.active = false
                THEN
                    UPDATE navigation n
                    SET active = false
                    WHERE n.content_id = OLD.id;
            end if;

            RETURN NEW;
        END;
    $$ LANGUAGE plpgsql;

    CREATE TRIGGER content_delete_trigger
	BEFORE UPDATE ON content
	FOR EACH ROW
	EXECUTE PROCEDURE content_delete();

COMMIT;

