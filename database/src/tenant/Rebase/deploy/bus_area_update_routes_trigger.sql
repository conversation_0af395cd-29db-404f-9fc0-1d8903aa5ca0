-- Deploy cm_tenant_db_RebaseSchema:bus_area_update_routes_trigger to pg

BEGIN;
    DROP TRIGGER IF EXISTS bus_area_trigger on bus_area;
    DROP FUNCTION IF EXISTS bus_area_delete();

    CREATE OR REPLACE FUNCTION bus_area_delete()
        <PERSON><PERSON><PERSON><PERSON> trigger AS $$
    BEGIN
        UPDATE bus_route br1 SET areas = array_remove(br2.areas, old.id)
        FROM bus_route br2
        WHERE br1.id = br2.id
        AND new.active = false
        AND array_position(br2.areas, old.id) is not null;
        RETURN NEW;
    END;
$$ LANGUAGE plpgsql;

    CREATE TRIGGER bus_area_trigger
	BEFORE UPDATE ON bus_area
	FOR EACH ROW
	WHEN (OLD.* IS DISTINCT FROM NEW.*)
	EXECUTE PROCEDURE bus_area_delete();

COMMIT;

