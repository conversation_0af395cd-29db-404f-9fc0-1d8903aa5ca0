-- Deploy cm_tenant_db_RebaseSchema:account_content_importer to pg

DO $$
BEGIN

    IF NOT EXISTS ( SELECT 1 FROM account WHERE id = '45f06f48-a93c-414e-b9a0-7582e0abc085') THEN
            INSERT INTO account (id, firstname, lastname, email, password, device_id, about, active)
            VALUES ('45f06f48-a93c-414e-b9a0-7582e0abc085','Integration', 'Importer', '<EMAIL>', '', '', '', false);
    END IF;

END; $$ language plpgsql;
