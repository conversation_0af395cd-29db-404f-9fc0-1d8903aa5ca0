%syntax-version=1.0.0
%project=cm_tenant_db_RebaseSchema
%uri=https://cm_tenant_db.local/Rebase_Schema

bus_area_update_routes_trigger 2021-02-11T18:01:08Z   <nick@nixCM> # implement trigger to update bus_routes on bus_area changes
content_update_navigation 2021-03-25T21:45:29Z   <nick@nixCM> # implement trigger on Content->inactive which sets all navigation relations to inactive
account_content_importer 2022-09-22T19:08:35Z developer <developer@imagineeverything-developer> # add a constant account across tenants to be used as importer
alert_templates 2022-05-02T18:56:44Z   <nick@nixCM> # sqitch out alert templates to tenants
2025-01-13-accounts_table_add_ie_staff 2025-01-14T06:31:58Z root <root@ca305d65d6c5> # add imagine everything team members to accounts tables
