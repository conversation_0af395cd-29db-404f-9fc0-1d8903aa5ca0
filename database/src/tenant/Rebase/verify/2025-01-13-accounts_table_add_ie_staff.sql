-- Verify cm_tenant_db_RebaseSchema:2025-01-13-accounts_table_add_ie_staff on pg
DO
$$
BEGIN
    IF NOT (
        SELECT COUNT(*)
        FROM account
        WHERE is_admin
          AND email = any (ARRAY [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
            ]::text[])
        ) = 14
        THEN
            RAISE EXCEPTION 'Accounts table did not contain Imagine Everything Team with is_admin ';
        end if;

    END
$$;
