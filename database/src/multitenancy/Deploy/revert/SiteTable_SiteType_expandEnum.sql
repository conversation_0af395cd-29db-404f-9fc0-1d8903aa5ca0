-- Revert cm_multitenancy_db_DeploySchema:SiteTable_SiteType_expandEnum from pg

BEGIN;

    UPDATE site set type = 'other'::site_type WHERE type = 'junior high - high school'::site_type;
    UPDATE site set type = 'other'::site_type WHERE type = 'elementary - junior high'::site_type;

    DELETE FROM pg_enum
    WHERE enumlabel = 'junior high - high school'
    AND enumtypid = (
        SELECT oid FROM pg_type WHERE typname = 'site_type'
    );

    DELETE FROM pg_enum
    WHERE enumlabel = 'elementary - junior high'
    AND enumtypid = (
        SELECT oid FROM pg_type WHERE typname = 'site_type'
    );

COMMIT;
