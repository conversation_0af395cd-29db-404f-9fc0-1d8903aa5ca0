-- Verify cm_multitenancy_db_DeploySchema:SiteTable_SiteType_expandEnum on pg
DO $$
BEGIN

    IF NOT EXISTS (SELECT 1 FROM pg_enum e JOIN pg_type t ON e.enumtypid = t.oid WHERE t.typname = 'site_type' and e.enumlabel = 'junior high - high school') THEN
        RAISE EXCEPTION 'site_type is missing the junior high - high school type';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_enum e JOIN pg_type t ON e.enumtypid = t.oid WHERE t.typname = 'site_type' and e.enumlabel = 'elementary - junior high') THEN
        RAISE EXCEPTION 'site_type is missing the elementary - junior high type';
    END IF;

END$$;
