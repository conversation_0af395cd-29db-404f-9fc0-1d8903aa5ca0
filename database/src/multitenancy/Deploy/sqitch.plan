%syntax-version=1.0.0
%project=cm_multitenancy_db_DeploySchema
%uri=https://cm_multitenancy_db.local/Deploy_Schema

BaseEnums 2020-07-29T14:42:36Z   <richard@nixCM> # Implement BaseEnums
TenantTable 2020-07-29T14:20:26Z   <richard@nixCM> # Implement Multi-tenancy Client Table
SiteTable 2020-07-29T14:25:21Z   <richard@nixCM> # Implement Multi-tenancy Site Table
DomainTable 2020-07-29T14:23:46Z   <richard@nixCM> # Implement Multi-tenancy Domain Table
SiteTable_tags 2020-10-07T15:06:50Z   <nick@nixCM> # implement tags [uuid] for sites
SiteTable_SiteType_expandEnum 2020-10-20T18:49:57Z   <richard@nixCM> # Implement additional values for site type enum
SiteTable_secure_settings 2020-11-09T18:36:33Z   <nick@nixCM> # implement secure_settings
SiteRelationshipLinkTable 2023-01-20T23:35:14Z developer <developer@imagineeverything-developer> # implement a sites relationship link table to indicate relationships between two sites
BaseEnums_siteType_department 2023-01-21T00:51:34Z developer <developer@imagineeverything-developer> # add department site type
2024-04-10-add_primary_domain 2024-04-10T21:09:30Z root <root@a14b3e3f9e93> # Adds the primary_domain column to site table
2024-12-04-Indexes 2024-12-05T00:49:16Z root <root@ddbd09656471> # add indexes
2024-12-13-tenant_add_admin_url 2024-12-13T20:58:39Z root <root@5c5e20c19c39> # add admin URL column to tenant table to point to their admin app
2025-01-07-site_drop_secure_settings_col 2025-01-08T05:44:22Z root <root@ca305d65d6c5> # drop secure_settings column from Site table
2025-01-14-GoogleEmailDomains 2025-01-14T18:46:29Z root <root@ca305d65d6c5> # add google admin account to email domain mapping table
2025-06-06-tenant_add_distributions 2025-06-06T19:05:11Z root <root@8fc63ea7e6ae> # Add column to tenant table for cloudfront distributions
