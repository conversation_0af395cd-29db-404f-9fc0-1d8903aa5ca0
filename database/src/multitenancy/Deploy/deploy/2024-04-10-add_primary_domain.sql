-- Deploy cm_multitenancy_db_DeploySchema:2024-04-10-add_primary_domain to pg
DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'site' and column_name = 'primary_domain') THEN
            ALTER TABLE public.site
                ADD COLUMN primary_domain VARCHAR(256);



            WITH ranked_domains AS (
                SELECT
                    d.site_id,
                    d.domain,
                    ROW_NUMBER() OVER (
                        PARTITION BY d.site_id
                        ORDER BY
                            d.domain NOT LIKE '%localhost%' DESC,
                            d.active DESC,
                            (d.domain NOT LIKE '%imagineeverything%' AND d.domain NOT LIKE '%cmdesign%') DESC,
                            d.is_primary DESC
                        ) AS rn
                FROM public.domain d
            ),
                 sites_with_domain AS (
                     SELECT
                         s.id AS site_id,
                         COALESCE(rd.domain, s.id::TEXT) AS primary_domain
                     FROM public.site s
                              LEFT JOIN ranked_domains rd ON s.id = rd.site_id AND rd.rn = 1
                 )
            UPDATE public.site s
            SET primary_domain = swd.primary_domain
            FROM sites_with_domain swd
            WHERE s.id = swd.site_id;

            CREATE UNIQUE INDEX on public.site(lower(public.site.primary_domain));
        END IF;
    END$$;

