-- Deploy cm_multitenancy_db_DeploySchema:ClientTable to pg

BEGIN;

	CREATE TABLE tenant
	(
		id uuid NOT NULL DEFAULT uuid_generate_v4(),
		name CHARACTER VARYING(256) NOT NULL,
		description TEXT,
		address CHARACTER VARYING(128),
		postal CHARACTER VARYING(64),
		city CHARACTER VARYING(64),
		province CHARACTER VARYING(64),
		country CHARACTER VARYING(64),
		created TIMESTAMP WITH TIME ZONE DEFAULT now(),
		modules INT DEFAULT 0,
		host CHARACTER VARYING(1024) NOT NULL,
		server CHARACTER VARYING(1024) NOT NULL,
		dbuser CHARACTER VARYING(1024) NOT NULL,
		dbpassword CHARACTER VARYING(1024) NOT NULL,
		settings jsonb DEFAULT '{}',
		active BOOLEAN NOT NULL DEFAULT true,
		CONSTRAINT pk_tenant_id PRIMARY KEY(id)

	)WITH (
		OIDS=FALSE
	);

	CREATE INDEX ix_tenant_name ON tenant (name);

	GRANT ALL ON TABLE tenant to contentmanager_application_user;

COMMIT;
