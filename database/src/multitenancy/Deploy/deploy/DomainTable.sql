-- Deploy cm_multitenancy_db_DeploySchema:DomainTable to pg

BEGIN;

	CREATE TABLE domain
	(
		id uuid NOT NULL DEFAULT uuid_generate_v4(),
 		tenant_id uuid NOT NULL,
 		site_id uuid NOT NULL,
 		domain character varying(256) NOT NULL,
 		is_primary boolean NOT NULL DEFAULT false,
 		is_site_default boolean DEFAULT false,
		active boolean NOT NULL DEFAULT true,
		CONSTRAINT pk_domain_id PRIMARY KEY(id),
		CONSTRAINT fk_domain_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenant(id) ON DELETE CASCADE,
		CONSTRAINT fk_domain_site_id FOREIGN KEY (site_id) REFERENCES site(id) ON DELETE CASCADE
	)WITH (
		OIDS=FALSE
	);

	CREATE INDEX ix_domain_name ON domain (domain);
	CREATE INDEX ix_domain_tenant_id ON domain (tenant_id);
	CREATE INDEX ix_domain_site_id ON domain (site_id);

	GRANT ALL ON TABLE domain to contentmanager_application_user;

COMMIT;
