-- Deploy cm_multitenancy_db_DeploySchema:2025-01-14-GoogleEmailDomains to pg

BEGIN;

    CREATE TABLE IF NOT EXISTS google_email_domains
    (
        email_domain CHARACTER VARYING(256) NOT NULL,
        admin_account_email CHARACTER VARYING(256) NOT NULL,
        CONSTRAINT pk_google_email_domains PRIMARY KEY(email_domain)
    );

    CREATE UNIQUE INDEX uq_google_email_domains_lower_email
        ON google_email_domains (lower(email_domain));

    GRANT ALL ON TABLE google_email_domains to contentmanager_application_user;

    insert into google_email_domains (email_domain, admin_account_email)
    values  ('ecacs16.ab.ca', '<EMAIL>'),
            ('ycs.nt.ca', '<EMAIL>'),
            ('esjs.nt.ca', '<EMAIL>'),
            ('esphs.nt.ca', '<EMAIL>'),
            ('weledeh.nt.ca', '<EMAIL>'),
            ('gshare.blackgold.ca', '<EMAIL>'),
            ('holyspirit.ab.ca', '<EMAIL>'),
            ('imagineeverything.com', '<EMAIL>'),
            ('fmcsd.ab.ca', '<EMAIL>'),
            ('fmcschools.ca', '<EMAIL>'),
            ('hfcrd.ab.ca', '<EMAIL>');
COMMIT;
