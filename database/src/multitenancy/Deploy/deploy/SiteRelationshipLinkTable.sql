-- Deploy cm_multitenancy_db_DeploySchema:SiteRelationshipLinkTable to pg

BEGIN;

    CREATE TABLE site_relationship
    (
		source_site_id uuid NOT NULL,
		target_site_id uuid NOT NULL,
		CONSTRAINT pk_site_relationship PRIMARY KEY(source_site_id, target_site_id),
        CONSTRAINT fk_site_relationship_source_site_id FOREIGN KEY (source_site_id) REFERENCES site(id) ON DELETE CASCADE,
        CONSTRAINT fk_site_relationship_target_site_id FOREIGN KEY (target_site_id) REFERENCES site(id) ON DELETE CASCADE
    )WITH (
		OIDS=FALSE
	);

    GRANT ALL ON TABLE site_relationship to contentmanager_application_user;

COMMIT;

