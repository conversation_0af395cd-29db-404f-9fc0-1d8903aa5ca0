-- Deploy cm_multitenancy_db_DeploySchema:SiteTable to pg

BEGIN;

	CREATE TABLE site
	(
		id uuid NOT NULL DEFAULT uuid_generate_v4(),
 		tenant_id uuid NOT NULL,
 		name CHARACTER VARYING(256) NOT NULL,
		description TEXT,
		type site_type NOT NULL,
		created TIMESTAMP WITH TIME ZONE DEFAULT now(),
		settings jsonb DEFAULT '{}',
		active boolean NOT NULL DEFAULT true,
		CONSTRAINT pk_site_id PRIMARY KEY(id),
		CONSTRAINT fk_site_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenant(id) ON DELETE CASCADE
	)WITH (
		OIDS=FALSE
	);

	CREATE INDEX ix_site_name ON site (name);
	CREATE INDEX ix_site_tenant_id ON site (tenant_id);

	GRANT ALL ON TABLE site to contentmanager_application_user;

COMMIT;