-- Revert cm_tenant_test_db_RebaseSchema:AccountData from pg

DO $$
BEGIN

IF EXISTS (SELECT 1 FROM current_database() WHERE current_database.name = 'cm_fallhill') THEN
	DELETE FROM Account WHERE id = '8987b4bc-ca85-4315-98b2-c35cf7b4966c';
END IF;

IF EXISTS (SELECT 1 FROM current_database() WHERE current_database.name = 'cm_imagineeverything') THEN
    DELETE FROM Account WHERE id = '39784ec8-8146-4578-be4a-cb2ee79ea901';
END IF;

END$$;