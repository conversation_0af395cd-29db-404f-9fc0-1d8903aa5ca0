-- Revert cm_tenant_test_db_RebaseSchema:SecurityGroupData from pg

DO $$
BEGIN

IF EXISTS (SELECT 1 FROM current_database() WHERE current_database.name = 'cm_fallhill') THEN
    DELETE FROM security_group WHERE id = '3013251a-387e-4590-bf3e-f1acbfb6b6d9';
    DELETE FROM security_group WHERE id = '222860bf-50bc-4a67-a511-25e74a26c10c';
END IF;

IF EXISTS (SELECT 1 FROM current_database() WHERE current_database.name = 'cm_imagineeverything') THEN
    DELETE FROM security_group WHERE id = '537c272f-c577-4ccf-838a-7c773ac18bde';
    DELETE FROM security_group WHERE id = 'f0c88346-88c3-45d7-b081-662088cbf93e';
END IF;

END$$;
