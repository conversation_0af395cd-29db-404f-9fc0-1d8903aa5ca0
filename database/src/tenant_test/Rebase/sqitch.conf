[core]
    engine = pg
    # plan_file = sqitch.plan
    # top_dir = .
    # deploy_dir = deploy
    # revert_dir = revert
    # verify_dir = verify
    # extension = sql

# [engine "pg"]a
    # target = db:pg:
    # registry = sqitch
    # client = psql

[target "dev_client"]
    uri = db:pg://postgres:apppass!@localhost/cm_tenant
[engine "pg"]
    target = dev_client
[add]
    template_directory = templates/

