-- Verify cm_tenant_test_db_RebaseSchema:AccountData on pg

DO $$
BEGIN

IF EXISTS (SELECT 1 FROM current_database() WHERE current_database.name = 'cm_fallhill') THEN
    IF NOT EXISTS (SELECT 1 FROM account where email = '<EMAIL>') THEN
		RAISE EXCEPTION 'test account missing from test data';
	END IF;
END IF;

IF EXISTS (SELECT 1 FROM current_database() WHERE current_database.name = 'cm_imagineeverything') THEN
    IF NOT EXISTS (SELECT 1 FROM account where email = '<EMAIL>') THEN
		RAISE EXCEPTION 'test account missing from test data';
	END IF;
END IF;

END$$;