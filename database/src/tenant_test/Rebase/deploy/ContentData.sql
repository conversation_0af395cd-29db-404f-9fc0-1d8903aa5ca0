-- Deploy contentmanagerapp_RebaseSchema:ContentData to pg

DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM content WHERE id = '41e2d015-2dd1-4670-9ec8-bd3dccdef2cb') THEN
            INSERT INTO content (id, type, title, content, data, pagelayout, route, path)
            VALUES('41e2d015-2dd1-4670-9ec8-bd3dccdef2cb','template','Template','<html>{{templateContent}}<html>',null, 'HTML','','41e2d0152dd146709ec8bd3dccdef2cb');
        END IF;
        IF NOT EXISTS (SELECT 1 FROM content WHERE id = 'c345766d-1308-4f6c-a28d-8f8abc69944a') THEN
            INSERT INTO content (id, type, title, content, data, pagelayout, route, path)
            VALUES('c345766d-1308-4f6c-a28d-8f8abc69944a','page','Page','<div>{{templateContent}}</div>',null, 'HTML',null,'41e2d0152dd146709ec8bd3dccdef2cb.c345766d13084f6ca28d8f8abc69944a');
        END IF;
        IF NOT EXISTS (SELECT 1 FROM content WHERE id = '0566fd30-08ee-45d6-a923-43fec538fb21') THEN
            INSERT INTO content (id, type, title, content, data, pagelayout, route, path)
            VALUES('0566fd30-08ee-45d6-a923-43fec538fb21','page','Homepage','<title>{{title}}</title>Homepage',null, 'HTML','/','41e2d0152dd146709ec8bd3dccdef2cb.c345766d13084f6ca28d8f8abc69944a.0566fd3008ee45d6a92343fec538fb21');
        END IF;
        IF NOT EXISTS (SELECT 1 FROM content WHERE id = '09d4453d-ca9f-439a-9235-c8bf2715cab4') THEN
            INSERT INTO content (id, type, title, content, data, pagelayout, route, path)
            VALUES('09d4453d-ca9f-439a-9235-c8bf2715cab4','page','Page2','<title>{{title}}</title>My Content',null, 'HTML','/mycontent','41e2d0152dd146709ec8bd3dccdef2cb.c345766d13084f6ca28d8f8abc69944a.09d4453dca9f439a9235c8bf2715cab4');
        END IF;
        IF NOT EXISTS (SELECT 1 FROM content WHERE id = '23150688-da97-460d-ac2f-7dc1ff0dc831') THEN
            INSERT INTO content (id, type, title, content, data, pagelayout, route, path)
            VALUES ('23150688-da97-460d-ac2f-7dc1ff0dc831', 'page', 'news', '<div>{{#newsRender News 6 3 "<div>" "</div>" }} {{#each Media}}<img src="/images/{{ Filename }}" width="50" height="50">{{/each}} {{ Title }} {{/newsRender}}</div>', null, 'HTML', '/news', '41e2d0152dd146709ec8bd3dccdef2cb.23150688da97460dac2f7dc1ff0dc831');
        END IF;
        IF NOT EXISTS (SELECT 1 FROM content WHERE id = 'ad166207-fe30-4b0a-ad79-9c0e3c2b9ca9') THEN
            INSERT INTO content (id, type, title, content, data, pagelayout, route, path)
            VALUES ('ad166207-fe30-4b0a-ad79-9c0e3c2b9ca9', 'news', 'Sept Is Crazy', '<div>My News Content</div>', null, 'HTML', '/news/septiscrazy', '41e2d0152dd146709ec8bd3dccdef2cb.ad166207fe304b0aad799c0e3c2b9ca9');
        END IF;
        IF NOT EXISTS (SELECT 1 FROM content WHERE id = '28a2a909-e7a4-4767-aeab-2df49ed8a469') THEN
            INSERT INTO content (id, type, title, content, data, pagelayout, route, path)
            VALUES ('28a2a909-e7a4-4767-aeab-2df49ed8a469', 'news', 'Oct Is Crazy', '<div>My News Content</div>', null, 'HTML', '/news/octiscrazy', '41e2d0152dd146709ec8bd3dccdef2cb.28a2a909e7a44767aeab2df49ed8a469');
        END IF;
        IF NOT EXISTS (SELECT 1 FROM content WHERE id = '093031c3-bbc9-46d1-8b4c-60f00433a600') THEN
            INSERT INTO content (id, type, title, content, data, pagelayout, route, path)
            VALUES ('093031c3-bbc9-46d1-8b4c-60f00433a600', 'news', 'Nov Is Crazy', '<div>My News Content</div>', null, 'HTML', '/news/noviscrazy', '41e2d0152dd146709ec8bd3dccdef2cb.093031c3bbc946d18b4c60f00433a600');
        END IF;
        IF NOT EXISTS (SELECT 1 FROM content WHERE id = '57387da6-1868-45bc-8463-e8c4e5f66664') THEN
            INSERT INTO content (id, type, title, content, data, pagelayout, route, path)
            VALUES ('57387da6-1868-45bc-8463-e8c4e5f66664', 'news', 'Dec Is Crazy', '<div>My News Content</div>', null, 'HTML', '/news/deciscrazy', '41e2d0152dd146709ec8bd3dccdef2cb.57387da6186845bc8463e8c4e5f66664');
        END IF;
        IF NOT EXISTS (SELECT 1 FROM content WHERE id = '0064d25b-0e79-4580-a3d6-3c7dc671e71c') THEN
            INSERT INTO content (id, type, title, content, data, pagelayout, route, path)
            VALUES ('0064d25b-0e79-4580-a3d6-3c7dc671e71c', 'page', 'DCT Page', '', '{"phone":"************"}', 'DCT', '/dctpage', '41e2d0152dd146709ec8bd3dccdef2cb.0064d25b0e794580a3d63c7dc671e71c');
        END IF;
END$$;



