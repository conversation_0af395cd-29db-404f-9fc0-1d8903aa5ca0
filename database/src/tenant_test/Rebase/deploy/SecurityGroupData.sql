-- Deploy cm_tenant_test_db_RebaseSchema:SecurityGroupData to pg

DO $$
BEGIN

IF EXISTS (SELECT 1 FROM current_database() WHERE current_database.name = 'cm_fallhill') THEN
    IF NOT EXISTS (SELECT 1 FROM security_group WHERE id = '3013251a-387e-4590-bf3e-f1acbfb6b6d9') THEN
        INSERT INTO security_group (id, name, description, role_id, site_id)
        VALUES ('3013251a-387e-4590-bf3e-f1acbfb6b6d9', 'Fallhill Board Office Content Manager Group', '', 'cf0dc324-c1c5-43d0-b348-219df40e69fa', null);
    END IF;
    IF NOT EXISTS (SELECT 1 FROM security_group WHERE id = '222860bf-50bc-4a67-a511-25e74a26c10c') THEN
        INSERT INTO security_group (id, name, description, role_id, site_id)
        VALUES ('222860bf-50bc-4a67-a511-25e74a26c10c', 'Fallhill Highschool Content Manager Group', '', 'cf0dc324-c1c5-43d0-b348-219df40e69fa', null);
    END IF;
END IF;

IF EXISTS (SELECT 1 FROM current_database() WHERE current_database.name = 'cm_imagineeverything') THEN
    IF NOT EXISTS (SELECT 1 FROM security_group WHERE id = '537c272f-c577-4ccf-838a-7c773ac18bde') THEN
        INSERT INTO security_group (id, name, description, role_id, site_id)
        VALUES ('537c272f-c577-4ccf-838a-7c773ac18bde', 'Imagine Everything Board Office Content Manager Group', '', 'cf0dc324-c1c5-43d0-b348-219df40e69fa', null);
    END IF;
    IF NOT EXISTS (SELECT 1 FROM security_group WHERE id = 'f0c88346-88c3-45d7-b081-662088cbf93e') THEN
        INSERT INTO security_group (id, name, description, role_id, site_id)
        VALUES ('f0c88346-88c3-45d7-b081-662088cbf93e', 'Imagine Everything HighSchool Content Manager Group', '', 'cf0dc324-c1c5-43d0-b348-219df40e69fa', null);
    END IF;
END IF;

END$$;