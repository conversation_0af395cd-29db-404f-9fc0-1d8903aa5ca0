-- Deploy cm_tenant_test_db_RebaseSchema:RoleData to pg

DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM role WHERE id = 'cf0dc324-c1c5-43d0-b348-219df40e69fa') THEN
            INSERT INTO role (id, name, active, mod_core, mod_pages, mod_news, mod_events, mod_media, mod_transportation, mod_alerts)
            VALUES ('cf0dc324-c1c5-43d0-b348-219df40e69fa', 'Content Manager Admin Role', true, 31, 3, 3, 3, 3, 3, 3);
        END IF;
END$$;
