-- Deploy cm_tenant_test_db_RebaseSchema:AccountData to pg

DO $$
BEGIN

IF EXISTS (SELECT 1 FROM current_database() WHERE current_database.name = 'cm_fallhill') THEN
    IF NOT EXISTS (SELECT 1 FROM account WHERE email = '<EMAIL>') THEN
        INSERT INTO account (id, firstname, lastname, email, password, active)
        VALUES ('8987b4bc-ca85-4315-98b2-c35cf7b4966c', 'Test', 'Account', '<EMAIL>', '$2a$10$UlTG95ghUljmDik./3ADruSNPcjBhImjUav8WKePVghW39fTqhRrm', true);

        INSERT INTO account_security_group (id, account_id, security_group_id)
        VALUES ('fd360876-1602-4997-a991-6a5c74d4b332', '8987b4bc-ca85-4315-98b2-c35cf7b4966c', '3013251a-387e-4590-bf3e-f1acbfb6b6d9');

         INSERT INTO account_security_group (id, account_id, security_group_id)
        VALUES ('a3411ff7-db32-46bd-b78a-07a5cdc2e59e', '8987b4bc-ca85-4315-98b2-c35cf7b4966c', '222860bf-50bc-4a67-a511-25e74a26c10c');
    END IF;
END IF;

IF EXISTS (SELECT 1 FROM current_database() WHERE current_database.name = 'cm_imagineeverything') THEN
    IF NOT EXISTS (SELECT 1 FROM account WHERE email = '<EMAIL>') THEN
        INSERT INTO account (id, firstname, lastname, email, password, active)
        VALUES ('39784ec8-8146-4578-be4a-cb2ee79ea901', 'Test', 'Account', '<EMAIL>', '$2a$10$UlTG95ghUljmDik./3ADruSNPcjBhImjUav8WKePVghW39fTqhRrm', true);

        INSERT INTO account_security_group (id, account_id, security_group_id)
        VALUES ('fd360876-1602-4997-a991-6a5c74d4b332', '39784ec8-8146-4578-be4a-cb2ee79ea901', '537c272f-c577-4ccf-838a-7c773ac18bde');

        INSERT INTO account_security_group (id, account_id, security_group_id)
        VALUES ('ab422aa2-1995-4447-9187-d92a7787e70c', '39784ec8-8146-4578-be4a-cb2ee79ea901', 'f0c88346-88c3-45d7-b081-662088cbf93e');
    END IF;
END IF;

END$$;