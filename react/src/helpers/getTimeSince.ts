import moment from 'moment'

export function getTimeSince(date: Date | string | number | moment.Moment): string {
    const targetMoment = moment(date)
    const now = moment()

    if (!targetMoment.isValid()) {
        throw new Error('Invalid date provided')
    }

    const diffInSeconds = Math.floor(now.diff(targetMoment, 'seconds'))

    // Handle future dates
    if (diffInSeconds < 0) {
        return targetMoment.format('MMM D, YYYY [at] h:mm A')
    }

    // Check if it's yesterday
    const isYesterday = targetMoment.isSame(moment().subtract(1, 'day'), 'day')

    if (diffInSeconds >= 86400) {
        if (isYesterday) {
            return `Yesterday at ${targetMoment.format('h:mm A')}`
        }

        // Show date with time for older items
        return targetMoment.format('MMM D, YYYY [at] h:mm A')
    }

    if (diffInSeconds < 60) {
        return `${diffInSeconds} seconds ago`
    }

    const minutes = Math.floor(diffInSeconds / 60)
    if (minutes < 60) {
        return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`
    }

    const hours = Math.floor(minutes / 60)
    return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`
}
