import { Details } from '@/pkgs/queries/types'
import { Template, TemplatesQuery } from '@/pkgs/queries/templates/types'
import React, { useState } from 'react'
import { BASE } from '@/common/constants'
import { useAppContext } from '@/pkgs/auth/atoms'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { CustomMenuItem } from '@/common/components/custom-context-menu/CustomMenu'
import { Preview } from '@mui/icons-material'
import CreateIcon from '@mui/icons-material/Create'
import { GridColDef } from '@mui/x-data-grid'
import { AdminCell, CellLine, CellWrapper, DateCell, MenuLightCell, TwoLinesCell } from '@/pkgs/grid/cells/GridCells'
import CircularProgress from '@mui/material/CircularProgress'
import Alert from '@mui/material/Alert'
import { DataGridBase } from '@/pkgs/grid/DataGridBase'
import { CreateTemplate } from '@/pkgs/queries/templates/CreateTemplate'
import { <PERSON><PERSON>, <PERSON><PERSON> } from '@mui/material'
import { useQueryTemplates } from '@/pkgs/queries/templates/queries'
import DeleteIcon from '@mui/icons-material/Delete'
import { httpDelete } from '@/common/client'
import { notify } from '@/helpers'

type TemplatesGridProps = {
    query: Details
    setTemplateToEdit: (
        value: ((prevState: Template | undefined) => Template | undefined) | Template | undefined
    ) => void
}

export const TemplatesGrid = ({ query, setTemplateToEdit }: TemplatesGridProps) => {
    const [state, setState] = useState<TemplatesQuery>({
        pageSize: 100,
        ...(query.StructureID && { StructureID: query.StructureID })
    })
    const [createTemplate, setCreateTemplate] = useState(false)
    const results = useQueryTemplates({ queryID: query.ID, q: state })

    const { menuItems } = useTemplates(query, setTemplateToEdit, () => results.refetch())

    if (results.isLoading) {
        return <CircularProgress />
    }

    if (results.isError) {
        return <Alert severity='error'>{guessErrorMessage(results.error)}</Alert>
    }

    const columns = getColumns(menuItems)

    return (
        <>
            <Stack direction='row' spacing={1} justifyContent='flex-end' alignItems='center' sx={{ mb: 2 }}>
                <Button variant={'contained'} onClick={() => setCreateTemplate(true)} size='small'>
                    Create Template
                </Button>
            </Stack>

            <DataGridBase filterMode={'client'} columns={columns} state={results.data} setQuery={setState} />

            {createTemplate && (
                <CreateTemplate
                    query={query}
                    open={createTemplate}
                    onClose={() => {
                        setCreateTemplate(false)
                        results.refetch()
                    }}
                />
            )}
        </>
    )
}

function useTemplates(
    query: Details,
    setTemplateToEdit: (
        value: ((prevState: Template | undefined) => Template | undefined) | Template | undefined
    ) => void,
    refetch: () => void
) {
    const appContext = useAppContext()
    const [selectedTemplate, setSelectedTemplate] = useState<Template | undefined>(undefined)

    const deleteTemplate = async (id: string) => {
        if (!confirm('Are you sure you want to delete this template?')) return

        try {
            await httpDelete(`${BASE}/api/v1/queries/${query.ID}/templates/${id}`)
        } catch (err) {
            notify(guessErrorMessage(err), 'error')
        }
    }

    const menuItems = (template: Template) => {
        return (onClose: () => void) => {
            return [
                <CustomMenuItem
                    key={'Preview'}
                    onClick={() => {
                        window.open(
                            `${BASE}/api/v1/queries/preview?nocache=${Math.random().toString()}&siteId=${
                                appContext.currentSiteID
                            }&queryID=${query.ID}&templateID=${template.ID}`,
                            '_blank'
                        )
                        onClose()
                    }}
                    text={'Preview'}
                >
                    <Preview />
                </CustomMenuItem>,
                <CustomMenuItem
                    key={'Edit'}
                    text={'Edit'}
                    onClick={() => {
                        setTemplateToEdit && setTemplateToEdit(template)
                        onClose()
                    }}
                >
                    <CreateIcon />
                </CustomMenuItem>,
                <CustomMenuItem
                    key={'Delete'}
                    text={'Delete'}
                    onClick={async () => {
                        await deleteTemplate(template.ID)
                        refetch()
                        onClose()
                    }}
                >
                    <DeleteIcon />
                </CustomMenuItem>
            ]
        }
    }

    return { selectedTemplate, setSelectedTemplate, menuItems }
}

function getColumns(menuItems: any): GridColDef[] {
    return [
        {
            field: 'Title',
            headerName: 'Title',
            flex: 1,
            filterable: false,
            sortable: true,
            renderCell: (params) => <TwoLinesCell l1={`${params.row.Title}`} l2={params.row.ID} />
        },
        {
            field: 'Type',
            headerName: 'Type',
            width: 100,
            filterable: false,
            sortable: false,
            renderCell: (params) => (
                <CellWrapper>
                    <CellLine>{params.row.Type}</CellLine>
                </CellWrapper>
            )
        },
        {
            field: 'UpdatedBy',
            headerName: 'Updated By',
            flex: 1,
            sortable: false,
            renderCell: (params) => <AdminCell ID={params.row.UpdatedBy} />
        },
        {
            headerName: 'Date',
            field: 'UpdatedAt',
            filterable: true,
            sortable: true,
            disableColumnMenu: false,
            width: 150,
            renderCell: (cellParams) => <DateCell {...cellParams} />
        },
        {
            field: 'Menu',
            headerName: '...',
            width: 80,
            sortable: false,
            filterable: false,
            renderCell: (params) => <MenuLightCell itemsFactory={menuItems(params.row)} />
        }
    ]
}
