import { z } from 'zod'
import { paged, PagingQuery, SortingQuery, trackable } from '@/common/react-query'

export type TemplatesQuery = { StructureID?: string } & PagingQuery & SortingQuery

export type BaseCellConfig = {
    linked: boolean
}

export type DateCellConfig = BaseCellConfig & {
    format: string
}

export type ImageCellConfig = BaseCellConfig & {
    width?: number
    height?: number
    crop?: '' | '1' | '2' | '3' | '4' | '5'
}

export type CellConfig = BaseCellConfig | DateCellConfig | ImageCellConfig

const tableCellSchema = z.object({
    Label: z.string(),
    Name: z.string(),
    Config: z
        .object({
            linked: z.boolean(),
            format: z.string().optional(),
            width: z.number().optional(),
            height: z.number().optional(),
            crop: z.enum(['', '1', '2', '3', '4', '5']).optional()
        })
        .default({ linked: false })
})

const templateBaseDTO = z.object({
    Title: z.string(),
    Description: z.string()
})

const createTemplateDTO = templateBaseDTO.extend({
    Type: z.union([z.literal('table'), z.literal('cards'), z.literal('custom')]),
    QueryID: z.string(),
    StructureID: z.string().nullable(),
    Data: z.any()
})

export const updateTemplateDTO = templateBaseDTO.extend({
    Data: z.any(),
    CSS: z.string(),
    JS: z.string(),
    ItemTemplate: z.string(),
    GroupTemplate: z.string(),
    WrapperTemplate: z.string()
})

export const viewSpecSchema = z.object({
    WrapperTemplate: z.string(),
    GroupTemplate: z.string(),
    ItemTemplate: z.string(),
    CSS: z.string(),
    JS: z.string()
})

export const templateSchema = z
    .object({
        ID: z.string(),
        Title: z.string(),
        Description: z.string(),
        Type: z.union([z.literal('table'), z.literal('cards'), z.literal('custom')]),
        QueryID: z.string(),
        StructureID: z.string().nullable(),
        Data: z.any()
    })
    .merge(trackable)

export const templatesSchema = paged.extend({
    Rows: z.array(templateSchema)
})

export type TableCell = z.infer<typeof tableCellSchema>
export type Template = z.infer<typeof templateSchema>
export type CreateTemplateDTO = z.infer<typeof createTemplateDTO>
export type UpdateTemplateDTO = z.infer<typeof updateTemplateDTO>
export type ViewSpec = z.infer<typeof viewSpecSchema>
