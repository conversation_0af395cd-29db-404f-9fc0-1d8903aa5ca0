import { Details, Field, UpdateQueryDTO } from '@/pkgs/queries/types'
import { getQueryType } from '@/pkgs/queries/QueryBuilder/utils/getQueryType'
import { getFields, sortFields } from '@/pkgs/queries/QueryBuilder/utils/fieldHelpers'
import { TableColumnsEditor } from '@/pkgs/queries/templates/TableColumnsEditor'
import React, { useEffect, useState } from 'react'
import { Template, updateTemplateDTO, UpdateTemplateDTO, viewSpecSchema } from '@/pkgs/queries/templates/types'
import { Button, Link, Stack, Tab, TextField } from '@mui/material'
import { DrawerComponent } from '@/pkgs/queries/QueryBuilder/components/DrawerComponent'
import { Box } from '@mui/system'
import { useQuery, useMutation } from '@tanstack/react-query'
import { httpGet, httpPut } from '@/common/client'
import { BASE } from '@/common/constants'
import Alert from '@mui/material/Alert'
import CircularProgress from '@mui/material/CircularProgress'
import { notify } from '@/helpers'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { baseQueryConfig } from '@/common/react-query'
import { TabContext, TabList, TabPanel } from '@mui/lab'
import { ViewSpecEditor } from '@/pkgs/queries/templates/ViewSpecEditor'
import { PostToNewWindow } from '@/common/components/PostToNewWindow'
import { useAppContext } from '@/pkgs/auth/atoms'
import { fieldsMap } from '@/pkgs/queries/QueryBuilder/utils/fieldsConfig'

type TemplateEditorProps = {
    query: Details
    queryDTO: UpdateQueryDTO
    template: Template | undefined
    onClose: () => void
}

export const TemplateEditor = ({ query, template, onClose, queryDTO }: TemplateEditorProps) => {
    const appContext = useAppContext()
    const [mainTab, setMainTab] = useState<'meta' | 'columns' | 'paging' | 'templating'>(
        template?.Type === 'table' ? 'columns' : 'templating'
    )
    const [viewSpecTab, setViewSpecTab] = useState<'wrapper' | 'group' | 'item' | 'css' | 'js'>('item')

    const queryType = getQueryType(query)

    const availableFields =
        queryType === 'image' // fieldsMap.media_id is a "virtual" column in Media table
            ? sortFields([...getFields(queryType, query.Structure), fieldsMap.media_id])
            : sortFields(getFields(queryType, query.Structure)).filter((f) => f.type !== 'array') // exclude array from tables cells

    const [state, setState] = useState<UpdateTemplateDTO | undefined>(undefined)
    const [errors, setErrors] = useState<Partial<Record<keyof UpdateTemplateDTO, string>>>({})

    const [drawerAnchor, setDrawerAnchor] = useState<'right' | 'top' | 'left' | 'bottom'>('right')

    const result = useQuery({
        queryKey: ['query-template', template?.ID],
        ...baseQueryConfig,
        enabled: !!template?.ID,
        queryFn: async () =>
            httpGet(`${BASE}/api/v1/queries/${query.ID}/templates/${template?.ID}`, null, updateTemplateDTO)
    })

    const viewSpecResult = useQuery({
        queryKey: ['query-template-view-spec', template?.ID],
        enabled: !!template?.Type,
        queryFn: async () =>
            httpGet(`${BASE}/api/v1/queries/${query.ID}/templates/defaults/${template?.Type}`, null, viewSpecSchema),
        cacheTime: Infinity,
        staleTime: Infinity
    })

    const mutation = useMutation({
        mutationFn: (data: UpdateTemplateDTO) =>
            httpPut(`${BASE}/api/v1/queries/${query.ID}/templates/${template?.ID}`, data),
        onSuccess: () => {
            notify('Template saved successfully', 'info')
            result.refetch()
        },
        onError: (error) => {
            notify(guessErrorMessage(error), 'error')
        }
    })

    useEffect(() => {
        if (!result.data) {
            return
        }

        setState(result.data)
    }, [result.data])

    if (!template) {
        return null
    }

    const tabs: {
        tab: 'wrapper' | 'group' | 'item' | 'css' | 'js'
        language: 'javascript' | 'html' | 'css'
        prop: keyof UpdateTemplateDTO
        snippets?: Array<{ label: string; content: string }>
    }[] = [
        {
            tab: 'item',
            language: 'html',
            prop: 'ItemTemplate',
            snippets: template.Type === 'table' ? [] : availableFields.map((f) => fieldToSnippet(f))
        },
        {
            tab: 'group',
            language: 'html',
            prop: 'GroupTemplate',
            snippets: []
        },
        {
            tab: 'wrapper',
            language: 'html',
            prop: 'WrapperTemplate',
            snippets: []
        },
        {
            tab: 'js',
            language: 'javascript',
            prop: 'JS',
            snippets: []
        },
        {
            tab: 'css',
            language: 'css',
            prop: 'CSS',
            snippets: []
        }
    ]

    return (
        <DrawerComponent open={!!template} onClose={onClose} onAnchorChange={(anchor) => setDrawerAnchor(anchor)}>
            <>
                {result.isLoading || (viewSpecResult.isLoading && <CircularProgress />)}
                {result.isError && <Alert severity={'error'}>guessErrorMessage(result.error)</Alert>}
                {viewSpecResult.isError && <Alert severity={'error'}>guessErrorMessage(viewSpecResult.error)</Alert>}
                {state && viewSpecResult.data && (
                    <Stack sx={{ height: '100%', overflow: 'hidden' }}>
                        <Stack
                            spacing={2}
                            direction={drawerAnchor === 'right' || 'left' ? 'column' : 'row'}
                            sx={{
                                p: 0,
                                m: 0,
                                height: '100%',
                                overflow: 'hidden'
                            }}
                        >
                            <Box
                                sx={{
                                    flex: 1,
                                    minHeight: 0, // This is crucial for flex child
                                    display: 'flex',
                                    flexDirection: 'column'
                                }}
                            >
                                <TabContext value={mainTab}>
                                    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                                        <TabList
                                            onChange={(e, newValue) => setMainTab(newValue)}
                                            sx={{
                                                minHeight: '32px',
                                                '& .MuiTab-root': {
                                                    minHeight: '32px',
                                                    padding: '6px 12px',
                                                    fontSize: '12px'
                                                }
                                            }}
                                        >
                                            {template?.Type === 'table' && <Tab label={'Columns'} value={'columns'} />}
                                            <Tab label={'Templating'} value={'templating'} />
                                            <Tab label={'Paging'} value={'paging'} />
                                            <Tab label={'Meta'} value={'meta'} />
                                        </TabList>
                                    </Box>
                                    <Box
                                        sx={{
                                            flex: 1,
                                            overflow: 'auto',
                                            minHeight: 0 // This is crucial for flex child
                                        }}
                                    >
                                        {template?.Type === 'table' && (
                                            <TabPanel value={'columns'} sx={{ p: 1 }}>
                                                <TableColumnsEditor
                                                    value={state.Data?.Cells || []}
                                                    options={availableFields}
                                                    onChange={(v) =>
                                                        setState({
                                                            ...state,
                                                            Data: { ...state.Data, Cells: v }
                                                        })
                                                    }
                                                />
                                            </TabPanel>
                                        )}

                                        <TabPanel value={'templating'} sx={{ height: 'calc(100% - 50px)' }}>
                                            <TabContext value={viewSpecTab}>
                                                <Box
                                                    sx={{
                                                        display: 'flex',
                                                        flexDirection: 'column',
                                                        height: '100%' // Take full height of parent
                                                    }}
                                                >
                                                    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                                                        <TabList
                                                            onChange={(e, newValue) => setViewSpecTab(newValue)}
                                                            sx={{
                                                                minHeight: '32px',
                                                                '& .MuiTab-root': {
                                                                    minHeight: '32px',
                                                                    padding: '6px 12px',
                                                                    fontSize: '12px'
                                                                }
                                                            }}
                                                        >
                                                            {tabs.map((tab) => (
                                                                <Tab key={tab.tab} label={tab.tab} value={tab.tab} />
                                                            ))}
                                                        </TabList>
                                                    </Box>

                                                    {tabs.map((tab, idx) => (
                                                        <TabPanel
                                                            key={tab.tab}
                                                            value={tab.tab}
                                                            sx={{ p: 1, flex: 1, overflow: 'auto' }}
                                                        >
                                                            <Stack
                                                                direction='column'
                                                                spacing={1}
                                                                sx={{ flex: 1, height: '100%' }}
                                                            >
                                                                {idx === 0 && query.ID !== template.QueryID && (
                                                                    <Alert severity={'warning'}>
                                                                        This template was initially created for{' '}
                                                                        <Link
                                                                            href={`/queries/${template.QueryID}`}
                                                                            target='_blank'
                                                                        >
                                                                            a different query
                                                                        </Link>
                                                                        .
                                                                    </Alert>
                                                                )}
                                                                <ViewSpecEditor
                                                                    value={state[tab.prop]}
                                                                    language={tab.language}
                                                                    defaultValue={viewSpecResult.data[tab.prop]}
                                                                    onChange={(v) =>
                                                                        setState({
                                                                            ...state,
                                                                            [tab.prop]: v || ''
                                                                        })
                                                                    }
                                                                    snippets={tab.snippets}
                                                                />
                                                            </Stack>
                                                        </TabPanel>
                                                    ))}
                                                </Box>
                                            </TabContext>
                                        </TabPanel>

                                        <TabPanel value={'paging'}>
                                            <Stack spacing={2}>
                                                <TextField
                                                    label='Page Size for the current template'
                                                    type={'number'}
                                                    value={state.Data?.PageSize || 0}
                                                    onChange={(e) =>
                                                        setState({
                                                            ...state,
                                                            Data: { ...state.Data, PageSize: parseInt(e.target.value) }
                                                        })
                                                    }
                                                    inputProps={{
                                                        min: 0,
                                                        max: 1000,
                                                        style: { width: '60px', flexGrow: 0, maxWidth: '60px' }
                                                    }}
                                                    size={'small'}
                                                    helperText={
                                                        'Number of items per page, if 0, default from the Query is used'
                                                    }
                                                />
                                            </Stack>
                                        </TabPanel>

                                        <TabPanel value={'meta'}>
                                            <Stack spacing={2}>
                                                <TextField
                                                    label='Title'
                                                    value={state.Title}
                                                    onChange={(e) => setState({ ...state, Title: e.target.value })}
                                                    error={!!errors.Title}
                                                    helperText={errors.Title}
                                                    size={'small'}
                                                />
                                                <TextField
                                                    label='Description'
                                                    multiline
                                                    rows={6}
                                                    value={state.Description}
                                                    onChange={(e) =>
                                                        setState({
                                                            ...state,
                                                            Description: e.target.value
                                                        })
                                                    }
                                                    size={'small'}
                                                />
                                                <Button
                                                    variant={'outlined'}
                                                    onClick={() => console.log(query)}
                                                    size={'small'}
                                                >
                                                    Query
                                                </Button>
                                            </Stack>
                                        </TabPanel>
                                    </Box>
                                </TabContext>
                            </Box>
                        </Stack>

                        <Stack direction={'row'} spacing={1} sx={{ p: 2 }}>
                            <Button
                                variant={'contained'}
                                onClick={() => {
                                    console.log(result.data)
                                    result.data && setState({ ...result.data })
                                }}
                                color={'warning'}
                                size={'small'}
                            >
                                Reset
                            </Button>
                            <Button
                                variant={'contained'}
                                onClick={async () => {
                                    if (!state) return
                                    mutation.mutate(state, {
                                        onSuccess: () => {
                                            onClose()
                                        }
                                    })
                                }}
                                disabled={mutation.isLoading}
                                size={'small'}
                                color={'success'}
                                style={{ marginLeft: 'auto' }}
                            >
                                {mutation.isLoading ? 'Saving...' : 'Save and Close'}
                            </Button>
                            <Button
                                variant={'contained'}
                                onClick={() => state && mutation.mutate(state)}
                                disabled={mutation.isLoading}
                                size={'small'}
                                color={'success'}
                            >
                                {mutation.isLoading ? 'Saving...' : 'Save'}
                            </Button>
                            <PostToNewWindow
                                url={`${BASE}/api/v1/queries/compile?nocache=${Math.random().toString()}&siteId=${
                                    appContext.currentSiteID
                                }`}
                                data={{
                                    Template: { ...template, ...state },
                                    Query: { ...query, ...queryDTO }
                                }}
                                target={`preview_window_${template?.ID}`}
                            >
                                <Button
                                    variant='contained'
                                    size='small'
                                    color={'info'}
                                    title={'Preview without saving'}
                                >
                                    Preview
                                </Button>
                            </PostToNewWindow>
                        </Stack>
                    </Stack>
                )}
            </>
        </DrawerComponent>
    )
}

function fieldToSnippet(field: Field): { label: string; content: string } {
    if (field.name === 'Sites' || field.name === 'Tags') {
        return {
            label: field.label,
            content: `{{>view prop="${field.name}" }}`
        }
    }

    let snippet = `{{ ${field.name} }}`

    if (field.name.includes('.')) {
        switch (field.type) {
            case 'contact-form-link':
                snippet = `{{>view prop="${field.name}" }}`
                break
            case 'checkbox':
                snippet = `{{#if Data.${field.name}}}Yes{{else}}No{{/if}}`
                break
            case 'image':
                const name = `Data.${field.name}`
                snippet = `
    {{#if ${name}}}
        {{>img src=${name}.src alt=${name}.alt rz-width=100 rz-height=100 rz-crop=1 }}
    {{/if}}
    `
                break
            case 'document':
                snippet = `<a href="{{ Data.${field.name} }}" target="_blank">${field.label}</a>`
                break
            default:
                snippet = `{{ Data.${field.name} }}`
                break
        }
    } else {
        switch (field.type) {
            case 'date':
                snippet = `{{ ${field.name}.Format 'Jan 2, 2006 3:04 PM' }}`
                break
            case 'image':
                snippet = `
    {{#if ${field.name}}}
        {{>img src=${field.name} alt=${field.label}.alt rz-width=100 rz-height=100 rz-crop=1 }}
    {{/if}}
    `
                break
            default:
                snippet = `{{ ${field.name} }}`
                break
        }
    }

    return {
        label: `${field.label}${field.name.includes('.') ? ` (${field.name})` : ''}`,
        content: snippet
    }
}
