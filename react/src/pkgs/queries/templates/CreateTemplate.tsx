import { Details } from '@/pkgs/queries/types'
import React, { useEffect, useState } from 'react'
import { CreateTemplateDTO } from '@/pkgs/queries/templates/types'
import { httpPost } from '@/common/client'
import { BASE } from '@/common/constants'
import { z } from 'zod'
import { notify } from '@/helpers'
import {
    Button,
    DialogActions,
    DialogContent,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextField
} from '@mui/material'
import CMDialog from '@/common/components/CMDialog'
import { fieldsMap } from '@/pkgs/queries/QueryBuilder/utils/fieldsConfig'

type CreateTemplateProps = {
    query: Details
    open: boolean
    onClose: () => void
}

const defaultCells = [
    { Label: 'Title', Name: 'Title' },
    { Label: 'Created At', Name: 'CreatedAt' }
]
const defaultCellsForImage = [
    { Label: 'Image', Name: 'MediaID', Config: { linked: true, width: 50, height: 50, crop: '1' } },
    { Label: 'Title', Name: 'Title' },
    { Label: 'Created At', Name: 'CreatedAt' }
]

export const CreateTemplate = ({ query, open, onClose }: CreateTemplateProps) => {
    const isImage = query.ContentTypes.some((type) => type === 'image')
    const [state, setState] = useState<CreateTemplateDTO>({
        Title: '',
        Description: '',
        Type: 'table',
        QueryID: query.ID,
        StructureID: query.StructureID,
        Data: {
            Cells: isImage ? defaultCellsForImage : defaultCells
        }
    })
    const [dirty, setDirty] = useState(false)
    const [loading, setLoading] = useState(false)
    const [errors, setErrors] = useState<Partial<Record<keyof CreateTemplateDTO, string>>>({})

    const validate = () => {
        setDirty(true)
        const newErrors: Partial<Record<keyof CreateTemplateDTO, string>> = {}
        if (!state.Title) newErrors.Title = 'Title is required'

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    useEffect(() => {
        if (!dirty) return
        validate()
    }, [state])

    const createTemplate = async () => {
        if (!validate()) {
            return null
        }

        try {
            setLoading(true)

            if (state.Type !== 'table') {
                state.Data = { Cells: [] }
            }

            const id = await httpPost(`${BASE}/api/v1/queries/${query.ID}/templates`, state, z.string())
            setLoading(false)
            return id
        } catch (error) {
            notify('Error creating template', 'error')
            setLoading(false)
        }
    }

    const handleCreate = async () => {
        const id = await createTemplate()
        if (!id) return
        onClose()
    }
    return (
        <CMDialog
            open={open}
            onClose={onClose}
            title={'Create Template'}
            showCloseButton={true}
            maxWidth='md'
            fullWidth
        >
            <DialogContent>
                <Stack spacing={2}>
                    <Stack spacing={2} direction='row' alignItems='center' sx={{ mb: 2 }}>
                        <FormControl sx={{ width: 150 }}>
                            <InputLabel id={'demo-simple-select-label'}>Type</InputLabel>
                            <Select
                                variant={'outlined'}
                                labelId='demo-simple-select-label'
                                label={'Type'}
                                value={state.Type}
                                error={!!errors.Type}
                                onChange={(event) => {
                                    setState({ ...state, Type: event.target.value as 'table' | 'cards' | 'custom' })
                                }}
                            >
                                <MenuItem value='table'>table</MenuItem>
                                {/*<MenuItem value='cards'>cards</MenuItem>*/}
                                <MenuItem value='custom'>custom</MenuItem>
                            </Select>
                        </FormControl>
                        <TextField
                            label='Title'
                            value={state.Title}
                            onChange={(e) => setState({ ...state, Title: e.target.value })}
                            error={!!errors.Title}
                            helperText={errors.Title}
                        />
                    </Stack>
                    <TextField
                        label='Description'
                        multiline
                        rows={6}
                        value={state.Description}
                        onChange={(e) => setState({ ...state, Description: e.target.value })}
                    />
                </Stack>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose} disabled={loading}>
                    Cancel
                </Button>
                <Button onClick={handleCreate} color='primary' disabled={loading} variant={'contained'}>
                    Create
                </Button>
            </DialogActions>
        </CMDialog>
    )
}
