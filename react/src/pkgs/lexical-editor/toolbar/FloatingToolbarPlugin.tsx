import { useCallback, useEffect, useRef } from 'react'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { mergeRegister } from '@lexical/utils'
import { $getSelection, $isRangeSelection, SELECTION_CHANGE_COMMAND } from 'lexical'
import { LowPriority, positionFloatingMenu } from './toolbar.helpers'
import { LexicalToolbar } from './LexicalToolbar'
import { colours } from '@/common/colours'
import { TOOLBAR_BUTTON } from '@/pkgs/lexical-editor/toolbar/getDefaultConfig'
import { INSERT_OPTIONS } from '@/pkgs/lexical-editor/toolbar/InsertOptionsDropdownList'
import { LexicalEditorOptions } from '../RichTextEditor'

interface FloatingToolbarProps {
    anchor: HTMLElement
    editorOptions?: LexicalEditorOptions
    disabled: boolean
}

export function FloatingToolbar({ anchor, editorOptions, disabled }: FloatingToolbarProps) {
    const [editor] = useLexicalComposerContext()

    const floatingToolbarRef = useRef<HTMLDivElement | null>(null)

    const updateFloatingToolbar = useCallback(() => {
        editor.getEditorState().read(() => {
            const selection = $getSelection()
            const nativeSelection = window.getSelection()
            const rootElement = editor.getRootElement()

            if (
                !editor.isComposing() &&
                $isRangeSelection(selection) &&
                selection &&
                nativeSelection &&
                !nativeSelection.isCollapsed &&
                rootElement?.contains(nativeSelection.anchorNode)
            ) {
                const selectedRangeNode = getSelectedRange(nativeSelection, rootElement)
                const editorInput = floatingToolbarRef.current?.parentElement
                if (!editorInput) {
                    positionFloatingMenu(floatingToolbarRef.current, selectedRangeNode, 'top-start', { y: -5, x: 0 })
                } else {
                    positionFloatingMenu(
                        floatingToolbarRef.current,
                        selectedRangeNode,
                        'top-start',
                        { y: -5, x: 0 },
                        editorInput as HTMLElement
                    )
                }
            } else {
                positionFloatingMenu(floatingToolbarRef.current, null)
            }
        })
    }, [editor])

    useEffect(() => {
        document.addEventListener('selectionchange', updateFloatingToolbar)
        return () => document.removeEventListener('selectionchange', updateFloatingToolbar)
    }, [updateFloatingToolbar])

    useEffect(() => {
        return mergeRegister(
            editor.registerUpdateListener(() => updateFloatingToolbar()),
            editor.registerCommand(
                SELECTION_CHANGE_COMMAND,
                (_payload, newEditor) => {
                    updateFloatingToolbar()
                    return true
                },
                LowPriority
            )
        )
    }, [editor, updateFloatingToolbar])

    return (
        <LexicalToolbar
            sx={{
                opacity: 0,
                top: '-10000px',
                left: '-10000px',
                position: 'absolute',
                borderColor: colours.off_white_but_darker,
                boxShadow: '0px 5px 10px rgba(0, 0, 0, 0.3);',
                borderRadius: '8px',
                transition: 'opacity 0.5s'
            }}
            variant='floating'
            ref={floatingToolbarRef}
            editorOptions={editorOptions}
            disabled={disabled}
        />
    )
}

export default function FloatingToolbarPlugin({
    anchor = document.body,
    editorOptions,
    disabled
}: {
    anchor?: HTMLElement
    editorOptions?: LexicalEditorOptions
    disabled: boolean
}): JSX.Element | null {
    return <FloatingToolbar disabled={disabled} editorOptions={editorOptions} anchor={anchor} />
}

function getSelectedRange(nativeSelection: Selection, rootElement: HTMLElement): HTMLElement {
    if (nativeSelection.anchorNode === rootElement) {
        let inner = rootElement
        while (inner.firstElementChild) {
            inner = inner.firstElementChild as HTMLElement
        }

        return inner
    }

    // @ts-ignore
    const result = nativeSelection.getRangeAt(0)?.startContainer?.parentNode || nativeSelection.focusNode?.parentNode

    if (result == rootElement) {
        // @ts-ignore
        return nativeSelection?.focusNode || result
    }

    // @ts-ignore
    return result
}
