import { $isAtNodeEnd } from '@lexical/selection'
import { computePosition, Placement } from '@floating-ui/react-dom'
import { MutableRefObject } from 'react'

export const LowPriority = 1
export const HighPriority = 0

export function getSelectedNode(selection) {
    const anchor = selection.anchor
    const focus = selection.focus
    const anchorNode = selection.anchor.getNode()
    const focusNode = selection.focus.getNode()
    if (anchorNode === focusNode) {
        return anchorNode
    }
    const isBackward = selection.isBackward()
    if (isBackward) {
        return $isAtNodeEnd(focus) ? anchorNode : focusNode
    } else {
        return $isAtNodeEnd(anchor) ? focusNode : anchorNode
    }
}

export async function positionFloatingMenu(
    $elementToPosition: HTMLElement | null,
    $anchorElement: HTMLElement | null,
    placement?: Placement,
    add?: {
        y: number
        x: number
    },
    containWithinElement?: HTMLElement
) {
    try {
        if ($elementToPosition === null) {
            return
        }
        if ($anchorElement === null) {
            $elementToPosition.style.opacity = '0'
            $elementToPosition.style.top = '-10000px'
            $elementToPosition.style.left = '-10000px'
        } else {
            let position = await computePosition($anchorElement, $elementToPosition, { placement })
            $elementToPosition.style.opacity = '1'

            if (!!containWithinElement) {
                // TODO: bottom, left, right boundary
                const containWithinElementRect = containWithinElement.getBoundingClientRect()
                const elementToPositionRect = $elementToPosition.getBoundingClientRect()
                if (
                    elementToPositionRect.left !== -10000 &&
                    elementToPositionRect.y - elementToPositionRect.height < containWithinElementRect.y
                ) {
                    // prevent $elementToPosition from overlapping with top boundary
                    placement = (placement?.replace('top', 'bottom') as Placement) || 'bottom'
                    position = await computePosition($anchorElement, $elementToPosition, { placement })
                    $elementToPosition.style.top = position.y + 7.5 + 'px'
                    $elementToPosition.style.left = position.x + 'px'
                    $elementToPosition.style.position = position.strategy
                    return
                }
            }
            $elementToPosition.style.top = position.y + (add?.y || 0) + 'px'
            $elementToPosition.style.left = position.x + (add?.x || 0) + 'px'
            $elementToPosition.style.position = position.strategy
        }
    } catch (e) {
        console.error(e)
    }
}

export async function $positionFloatingMenu(
    $elementToPosition: MutableRefObject<null>,
    $anchorElement: MutableRefObject<null>,
    placement?: Placement,
    add?: {
        y: number
        x: number
    }
) {
    return positionFloatingMenu($elementToPosition.current, $anchorElement.current, placement, add)
}
