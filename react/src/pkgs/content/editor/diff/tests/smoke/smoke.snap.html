{
  "Data": {
    "ALLOWMULTIPLETEST": {
      "modifiedValue": [
        ,
        {
          "example checkbox": true,
          "example select": "option 1 value",
          "lexical": {
            "html": "<p class="editor-paragraph" dir="ltr"><span style="white-space: pre-wrap;">modified allowed multiple RTE</span></p>",
            "json": {
              "root": {
                "children": [
                  {
                    "children": [
                      {
                        "text": "modified allowed multiple RTE",
                      },
                    ],
                    "direction": "ltr",
                  },
                ],
                "direction": "ltr",
              },
            },
          },
        },
        {
          "derp image": {
            "alt": "",
            "src": "/images/b41e29ef-91cd-5209-b541-45fa84a71c6b",
          },
          "example checkbox": true,
          "example select": "option 1 value",
          "id": "ad22b86a-6c3a-4487-aa05-dedb5e7259c3",
          "lexical": {
            "engine": "lexical",
            "html": "<p class="editor-paragraph" dir="ltr"><span style="white-space: pre-wrap;">additional value</span></p>",
            "json": {
              "root": {
                "children": [
                  {
                    "children": [
                      {
                        "detail": 0,
                        "format": 0,
                        "mode": "normal",
                        "style": "",
                        "text": "additional value",
                        "type": "text",
                        "version": 1,
                      },
                    ],
                    "direction": "ltr",
                    "format": "",
                    "indent": 0,
                    "textFormat": 0,
                    "textStyle": "",
                    "type": "cm-paragraph",
                    "version": 1,
                  },
                ],
                "direction": "ltr",
                "format": "",
                "indent": 0,
                "type": "root",
                "version": 1,
              },
            },
          },
        },
      ],
      "originalValue": [
        ,
        {
          "example checkbox": false,
          "example select": "option 3 value",
          "lexical": {
            "html": "<p class="editor-paragraph"><span style="white-space: pre-wrap;">212341</span></p>",
            "json": {
              "root": {
                "children": [
                  {
                    "children": [
                      {
                        "text": "212341",
                      },
                    ],
                    "direction": null,
                  },
                ],
                "direction": null,
              },
            },
          },
        },
      ],
    },
    "jobInfo": {
      "contract": {
        "modifiedValue": "current contract #",
        "originalValue": "asfasfasfasdfasdfasdf",
      },
      "date": {
        "modifiedValue": "2025-06-12",
        "originalValue": "2025-04-17",
      },
      "fte": {
        "modifiedValue": "current FTE",
        "originalValue": "asfasfasfasdfasdfasdfasdf",
      },
      "image1": {
        "modifiedValue": {
          "alt": "",
          "src": "/images/73199f1d-fbbd-594b-8b2b-74bc4a4cc4b4",
        },
        "originalValue": {
          "alt": "",
          "src": "/images/c5d45843-b1b9-5d19-a031-eb59f4ca0324",
        },
      },
      "lexical": {
        "modifiedValue": "<p class="editor-paragraph" dir="ltr">
	<span style="white-space: pre-wrap;">current rte</span>
</p>",
        "originalValue": "<p class="editor-paragraph" dir="ltr">
	<span style="white-space: pre-wrap;">asfasfasfasfasfasf</span>
	<br>
	<span style="white-space: pre-wrap;">&nbsp;</span>
</p>
<p class="editor-paragraph" dir="ltr">
	<span style="white-space: pre-wrap;">asdfasdf</span>
</p>
<p class="editor-paragraph" dir="ltr">
	<span style="white-space: pre-wrap;">asfasfasfasf</span>
</p>
<p class="editor-paragraph">
	<span style="white-space: pre-wrap;">&nbsp;</span>
</p>
<p class="editor-paragraph" dir="ltr">
	<span style="white-space: pre-wrap;">asdfasdfsadf</span>
</p>",
      },
      "location": {
        "modifiedValue": "current location",
        "originalValue": "asfasfasf",
      },
      "term": {
        "modifiedValue": "current term",
        "originalValue": "Par time Regularasfasf",
      },
      "textarae": {
        "modifiedValue": "current textarea",
        "originalValue": undefined,
      },
    },
  },
  "ExpireAt": {
    "modifiedValue": "2025-04-15T13:45:01.06-07:00",
    "originalValue": null,
  },
  "MediaID": {
    "modifiedValue": "b41e29ef-91cd-5209-b541-45fa84a71c6b",
    "originalValue": null,
  },
  "PrivacyLevel": {
    "modifiedValue": 2,
    "originalValue": 0,
  },
  "Route": {
    "modifiedValue": "/educational-assistant11",
    "originalValue": "/educational-assistant",
  },
  "Settings": {
    "modifiedValue": {
      "isDistrictPage": true,
      "seoDescription": "",
      "seoTitle": "",
    },
    "originalValue": {},
  },
  "Sites": {
    "modifiedValue": [
      "7ee32940-8115-584e-a991-1d5a3be39a47",
      "9cb1a469-1931-55ff-9e9e-bd185b56149a",
      "740a8138-c9f7-5ac0-8d6b-a5a352149e31",
    ],
    "originalValue": [
      "7ee32940-8115-584e-a991-1d5a3be39a47",
    ],
  },
  "Tags": {
    "modifiedValue": [
      "e2c786ca-1026-4dd8-ad81-e30f723acca4",
      "3686313a-ad9f-4ad4-a893-f9bfd0ab9f61",
    ],
    "originalValue": [
      "e2c786ca-1026-4dd8-ad81-e30f723acca4",
      "3686313a-ad9f-4ad4-a893-f9bfd0ab9f61",
      "3711d49b-aa7a-4517-a825-cd3a2bd38839",
      "391f9649-d1c6-4a32-bb73-8d57f05898a8",
    ],
  },
  "Title": {
    "modifiedValue": "Current title",
    "originalValue": "Educational Assistant (Division Office)",
  },
}