import { useEffect, useRef, useState } from 'react'
import { Content } from '../types'
import { useParams } from 'react-router-dom'
import { colours } from '@/common/colours'
import { Box, FormControlLabel, Tooltip, Typography } from '@mui/material'
import CheckIcon from '@mui/icons-material/Check'
import SaveIcon from '@mui/icons-material/Save'
import { useStateWithStorage } from '@/common/storage.service'
import { LoadingSpinner } from '@/common/components/CenteredSpinner'
import { atom, useAtom, useAtomValue, useSetAtom } from 'jotai'
import { getTimeSince } from '@/helpers/getTimeSince'
import { PrettySwitch } from '@/common/components/PrettySwitch'
export const CONTENT_EDITOR_AUTO_SAVE_KEY = 'content-editor-auto-save'

export const autoSaveAtom = atom<boolean>(false)
interface UseAutoSaveProps {
    hasChanges: boolean
    data: Content | undefined
    onSave: (data: Content) => void
    wait?: number
}

export function useAutoSave({ hasChanges, data, onSave, wait = 2000 }: UseAutoSaveProps) {
    const autoSaveAtomIsEnabled = useAtomValue(autoSaveAtom)
    const { workspace } = useParams()

    const debouncedData = useDebounce(data, wait)

    useEffect(() => {
        if (hasChanges && debouncedData && workspace !== 'live' && workspace !== 'branch' && autoSaveAtomIsEnabled) {
            onSave(debouncedData)
        }
    }, [debouncedData, workspace, autoSaveAtomIsEnabled])

    return autoSaveAtomIsEnabled
}

export function useDebounce<TData>(data: TData, interval: number) {
    const [liveData, setLiveData] = useState<TData>(data)

    useEffect(() => {
        if (typeof window !== 'undefined') {
            const handler = setTimeout(() => {
                setLiveData(data)
            }, interval)
            return () => {
                clearTimeout(handler)
            }
        }
    }, [data, interval])

    return liveData
}

// auto save loading icon
export function AutoSaveIcon({
    success,
    error,
    loading,
    lastUpdated
}: {
    success: boolean
    error: boolean
    loading: boolean
    lastUpdated: Date
}) {
    const [isEnabled, setIsEnabled] = useStateWithStorage(CONTENT_EDITOR_AUTO_SAVE_KEY, true)
    const [autoSaveAtomIsEnabled, setAutoSaveAtomIsEnabled] = useAtom(autoSaveAtom)
    const isSaved = (success || !loading) && !error
    // loading = show saving icon and circular progress
    // just finished = show GREEN checkmark
    // resting state = show GREY checkmark
    const timer = useRef<ReturnType<typeof setTimeout> | undefined>(undefined)

    useEffect(() => {
        return () => {
            clearTimeout(timer.current)
        }
    }, [])

    useEffect(() => {
        if (isEnabled !== autoSaveAtomIsEnabled) {
            setAutoSaveAtomIsEnabled(isEnabled)
        }
    }, [isEnabled])

    return (
        <Box sx={{ display: 'flex' }}>
            <Tooltip
                title={
                    !!error
                        ? 'Unable to save.'
                        : !isEnabled
                          ? 'Auto Save is disabled. Click to enable'
                          : isSaved
                            ? `Editor is up to date. Last updated: ${getTimeSince(lastUpdated)}`
                            : 'Saving'
                }
            >
                <Box sx={{ display: 'flex', marginLeft: '.4rem', alignItems: 'center' }}>
                    {isEnabled ? (
                        <Box
                            sx={{
                                position: 'relative',
                                marginRight: '.4rem',
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}
                        >
                            {isSaved ? (
                                <CheckIcon color='success' />
                            ) : (
                                <SaveIcon color={!!error ? 'error' : 'secondary'} />
                            )}
                            {loading && (
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        top: -4,
                                        left: -4,
                                        zIndex: 1
                                    }}
                                >
                                    <LoadingSpinner size={32} />
                                </Box>
                            )}
                        </Box>
                    ) : (
                        <></>
                    )}
                    <FormControlLabel
                        sx={{ margin: 0 }}
                        label={'Auto-save'}
                        labelPlacement='start'
                        control={
                            <PrettySwitch
                                color='primary'
                                sx={{ marginLeft: '0.4rem' }}
                                size={'small'}
                                checked={isEnabled}
                                onChange={(e) => {
                                    setIsEnabled(e.target.checked)
                                }}
                            />
                        }
                    />
                </Box>
            </Tooltip>
        </Box>
    )
}
