import { deleteContentQuery, restoreContentQuery, useContentQueries } from '../queries'
import { useParams } from 'react-router-dom'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { BaseTemplate, TemplateSelector } from './components/TemplateSelector'
import FormRenderer from '../../form-renderer/FormRenderer'
import { StructureSelector } from '../../structure/StructureSelector'
import { FormStructure } from '../../structure/types'
import { asSecured } from '../../auth/permissions/securityMapping'
import {
    Alert,
    Box,
    Button,
    Card,
    Checkbox,
    FormControl,
    FormControlLabel,
    FormLabel,
    Grid,
    Typography
} from '@mui/material'
import TextField from '@mui/material/TextField'
import { RouteEditor } from './components/RouteEditor'
import { SeoOptions } from '@/common/components/SeoOptions'
import AppAccordion from '../../../common/components/AppAccordion'
import { GoToNavigation } from '../../navigation/GoToNavigation'
import { useAppContext, useCurrentSite } from '../../auth/atoms'
import { LegacyUrls } from './components/LegacyUrls'
import { DistributedPageEditor } from '../distributed-page/DistributedPageEditor'
import { EntityScopeEnum } from '../../auth/entityScope'
import { EventDateTimePickerV2 } from '@/common/editor/EventDateTimePickerV2'
import { MetaEditor } from './components/MetaEditor'
import { ContentEditorSaveBar } from './ContentEditorSaveBar'
import { useStructuredContentValidators } from './useStructuredContentValidators'
import { Content, ContentType, PublishPeriod } from '../types'
import { useDisable } from '@/common/useDisable'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { useInputDisabler } from './imported-content/useInputDisabler'
import { EditableFieldsList } from './imported-content/EditableFieldsList'
import { BaseForm } from '../BaseForm'
import { AlertNotification } from './components/AlertNotification'
import { PinNews } from '@/pkgs/content/editor/components/PinNews'
import { RevisionHistoryList } from '@/pkgs/content/RevisionHistoryList'
import { bannerHeightAtom } from '@/app/AppTopBar/InstagramErrorsBanner'
import { appTopBarHeightAtom } from '@/app/AppTopBar/AppTopBar'
import { useAtom } from 'jotai'
import PageContainer from '@/common/components/PageContainer'
import useContentDiff from './diff/useContentDiff'
import { DistributedListsForContent } from '@/pkgs/ordered-lists/distributed-lists/DistributedListsForContent'
import { ListsForContent } from '@/pkgs/ordered-lists/distributed-lists/ListsForContent'
import { useStructureByIdQuery } from '@/pkgs/structure/queries'
import { TagsSelector } from '@/pkgs/system/tags/TagsSelector'
import { SearchTools } from '@/pkgs/search/SearchTools'
import { ContentIndexingConfig } from '@/pkgs/search/types'
import { ContentEditorLoading } from './ContentEditorLoading'
import notify from '@/helpers/notify'
import { useURLParamMenuItem } from '@/pkgs/system/menu-builder/useURLParamMenuItem'
import { useFieldRestriction } from '@/pkgs/auth/permissions/field_restriction'
import PublishStatusLabel from '@/pkgs/content/editor/components/PublishStatusLabel'
import { useSidebarToggle } from '@/pkgs/content/editor/useSidebarToggle'
import { MinimizeSidebarButton } from '@/pkgs/content/editor/MinimizeSidebarButton'

export const defaultSEOAccordionId = 'defaultSEOAccordion'
export const defaultMetaAccordionId = 'defaultMetaAccordion'

function getDefaultAccordionState(expanded = true) {
    return {
        [defaultSEOAccordionId]: expanded,
        [defaultMetaAccordionId]: expanded === undefined ? false : expanded
    }
}

export const ContentEditor = () => {
    const { id, workspace } = useParams()
    const { fetcher, updater, brancher, autoSaveUpdater } = useContentQueries({ id, workspace })

    const appContext = useAppContext()
    const currentSite = useCurrentSite()

    const { data: cdi } = useURLParamMenuItem()
    const canUpdateDistributed = appContext.action(
        {
            Sites: null,
            DepartmentID: null,
            EntityScope: EntityScopeEnum.List
        },
        'update'
    )

    const [isEditMode, setIsEditMode] = useState<boolean>(false)
    const [isFirstTimeLoading, setIsFirstTimeLoading] = useState(true)

    useEffect(() => {
        if (isEditMode) {
            if (isFirstTimeLoading) {
                // Initial loading, no need to refetch
                setIsFirstTimeLoading(false)
                return
            }

            fetcher.refetch()
            setContentEditorIsLoading(true)
        }
    }, [isEditMode])

    const contentEditorRef = useRef<HTMLDivElement | null>(null)
    const { isMinimized, toggle, left } = useSidebarToggle(contentEditorRef)
    const [bannerHeight] = useAtom(bannerHeightAtom)
    const [appTopBarHeight] = useAtom(appTopBarHeightAtom)

    const [contentEditorIsLoading, setContentEditorIsLoading] = useState(false)

    useEffect(() => {
        if (contentEditorIsLoading) {
            setTimeout(() => {
                setContentEditorIsLoading(false)
            }, 1000)
        }
    }, [contentEditorIsLoading])

    const formRendererRef = useRef<any>()
    const [accordionExpanded, setAccordionExpanded] = useState(getDefaultAccordionState(true))

    function setAllAccordions(expanded) {
        formRendererRef?.current?.setAllAccordions(expanded)
        setAccordionExpanded(getDefaultAccordionState(expanded))
    }

    const [serverValue, setServerValue] = useState<Content | undefined>(undefined)
    const [state, setState] = useState<Content | undefined>(undefined)
    const [action, setAction] = useState<'branch' | 'update'>('update')
    const isFieldRestricted = useFieldRestriction(state)

    // stateStructure is used for initial loading when structure selector is not rendered (sidebarIsMinimized == true)
    const { data: stateStructure } = useStructureByIdQuery(state?.StructureID || '')

    const [structure, setStructure] = useState<FormStructure[] | undefined>(undefined)
    const [selectedTemplate, setSelectedTemplate] = useState<BaseTemplate | undefined>(undefined)
    const allowedStructures = useMemo(() => {
        if (cdi?.Structures?.length) {
            return selectedTemplate?.Structures?.filter((structureId) => cdi?.Structures?.includes(structureId)) || []
        }

        return selectedTemplate?.Structures || []
    }, [selectedTemplate, cdi])

    const [distributedPageHasChanges, setDistributedPageHasChanges] = useState(false)

    const { validateAll, errors, setErrors } = useStructuredContentValidators({
        componentsToValidate: [
            'Sites',
            'Route',
            'Title',
            'Path',
            'StructureID',
            'Settings',
            'PublishAt',
            'ExpireAt',
            'Tags'
        ],
        state: state,
        formRendererRef: formRendererRef,
        cdi: cdi
    })

    const [notificationErrors, setNotificationErrors] = useState<Partial<Record<string, string>>>({})
    const validateNotification = () => {
        const validationErrors: Partial<Record<string, string>> = {}
        if (state?.Settings?.HasEmailNotification) {
            if (!state?.Settings?.EmailSubject) {
                validationErrors.EmailSubject = 'Email Subject is required'
            }
            if (!state?.Settings?.EmailBody) {
                validationErrors.EmailBody = 'Email Body is required'
            }
        }
        setNotificationErrors(validationErrors)
        return Object.keys(validationErrors).length === 0
    }

    const { changed, renderContentDiffViewer, setContentDiffViewerIsOpen } = useContentDiff({
        serverValue: serverValue,
        value: state,
        hasExternalChanges: distributedPageHasChanges,
        title: 'Saved State',
        onRevert: (newValue) => {
            console.log('onRevert', newValue)
            setState({ ...newValue })
            setContentEditorIsLoading(true)
        }
    })

    const classifications = useMemo(
        () => (serverValue?.Type === 'page' ? ['page', 'template'] : [serverValue?.Type || 'page']),
        [serverValue]
    )

    useDisable()

    const hasPermission = useMemo(() => appContext.action(state, 'update'), [state])
    const hasDistributedPagePermission = useMemo(
        () =>
            appContext.action(
                { EntityScope: EntityScopeEnum.Page, Sites: [currentSite?.ID], DepartmentID: null },
                'update'
            ),
        [currentSite]
    )

    const { isInputDisabled, isImported, importInfo } = useInputDisabler({ content: state, hasPermission, isEditMode })

    useEffect(() => {
        if (updater.error) {
            const errorMessage = guessErrorMessage(updater.error)
            if (errorMessage.includes('Route')) {
                setErrors((prev) => ({ ...prev, Route: errorMessage }))
            }
            formRendererRef?.current?.processServerError(updater.error)
        } else {
            setErrors((prev) => ({ ...prev, Route: '' }))
        }
    }, [updater.error])

    useEffect(() => {
        if (!workspace) return

        if (!fetcher.data || fetcher.isLoading || fetcher.isRefetching) {
            setState(undefined)
            setServerValue(undefined)
            return
        }

        if (fetcher.data.Workspace === workspace) {
            setAction('update')
            setState(fetcher.data)
            setServerValue(fetcher.data)
            return
        }

        // Data is loaded but workspace does not match
        const updatedData = {
            ...fetcher.data,
            Workspace: workspace,
            Active: true
        }
        setAction('branch')
        setIsEditMode(true)
        setState(updatedData)
        setServerValue(updatedData)
    }, [fetcher.data, workspace])

    useEffect(() => {
        if (!state) return
        if (!!cdi) {
            document.title = `* ${state.Title}`
        } else {
            document.title = state.Title
        }
    }, [state?.Title])

    return (
        <Box>
            {serverValue && state && (
                <ContentEditorSaveBar
                    queryStatus={
                        updater.isLoading
                            ? 'loading'
                            : updater.isError || !!Object.values(errors).find((v) => !!v.length)
                              ? 'error'
                              : updater.isSuccess
                                ? 'success'
                                : undefined
                    }
                    serverValue={serverValue}
                    value={state}
                    saveAction={action}
                    hasExternalChanges={distributedPageHasChanges}
                    onAction={(command, state) => {
                        if (command === 'refetch') {
                            fetcher.refetch()
                            return
                        }

                        if (command === 'delete') {
                            if (window.confirm('Are you sure you want to delete this page?')) {
                                deleteContentQuery(state.ID, state.Workspace)
                                    .then(() => {
                                        notify('Page deleted', 'info')
                                        fetcher.refetch()
                                    })
                                    .catch((err) => {
                                        notify(guessErrorMessage(err), 'error')
                                    })
                            }
                            return
                        }

                        if (command === 'restore') {
                            if (window.confirm('Are you sure you want to restore this page?')) {
                                restoreContentQuery(state.ID)
                                    .then(() => {
                                        notify('Page restored', 'info')
                                        fetcher.refetch()
                                    })
                                    .catch((err) => {
                                        notify(guessErrorMessage(err), 'error')
                                    })
                            }
                            return
                        }

                        if (!validateAll() || !validateNotification()) {
                            return
                        }

                        if (command === 'save' && hasPermission) {
                            action === 'branch' ? brancher.mutate(state) : updater.mutate(state)
                        }

                        if (command === 'auto-save' && hasPermission) {
                            autoSaveUpdater.mutate(state)
                        }
                    }}
                    disabled={!hasPermission || !isEditMode || fetcher.isLoading}
                    onChange={(publishPeriod: PublishPeriod) => {
                        setState({ ...state, PublishAt: publishPeriod.PublishAt, ExpireAt: publishPeriod.ExpireAt })
                    }}
                    onChangeMode={async (checked) => {
                        setIsEditMode(checked)
                    }}
                    onRevert={(newValue) => {
                        setState({ ...newValue })
                        setContentEditorIsLoading(true)
                    }}
                />
            )}

            {contentEditorIsLoading && <ContentEditorLoading />}

            {!contentEditorIsLoading && (
                <PageContainer>
                    {fetcher.isLoading && <div>Loading...</div>}
                    {fetcher.isError && <div>Error: {fetcher.error.message}</div>}

                    {state && (
                        <Grid container spacing={2} marginTop={`${bannerHeight + appTopBarHeight}px`}>
                            <Grid
                                container
                                item
                                xs={isMinimized ? 0 : 8}
                                sx={{ mb: 30 }}
                                data-testid='content-editor-main'
                                mt={'-12px'}
                                ref={contentEditorRef}
                            >
                                <Grid item xs={12}>
                                    <MinimizeSidebarButton isMinimized={isMinimized} toggle={toggle} left={left} />

                                    {(structure || stateStructure?.FormStructure) && (
                                        <FormRenderer
                                            ref={formRendererRef}
                                            value={state.Data}
                                            onChange={(d) => {
                                                setState({ ...state, Data: d })
                                            }}
                                            formStructure={structure || stateStructure?.FormStructure}
                                            isFieldRestricted={isFieldRestricted}
                                            disabled={isInputDisabled('data')}
                                            accordionExpandedOnChange={(id, expanded) => {
                                                if (Array.isArray(id)) {
                                                    setAccordionExpanded({
                                                        ...accordionExpanded,
                                                        ...(id.reduce(
                                                            (a, id, index) => ({
                                                                ...a,
                                                                [id]: expanded
                                                            }),
                                                            {}
                                                        ) || {})
                                                    })
                                                } else {
                                                    setAccordionExpanded({
                                                        ...accordionExpanded,
                                                        [id]: expanded
                                                    })
                                                }
                                            }}
                                        />
                                    )}

                                    {currentSite?.ID &&
                                        !!state.Settings?.isDistrictPage &&
                                        state.Workspace === 'live' && (
                                            <DistributedPageEditor
                                                parentID={state.ID}
                                                siteID={currentSite?.ID}
                                                onChanges={(hasChanges) => {
                                                    setDistributedPageHasChanges(hasChanges)
                                                }}
                                                label={`${state.Title} for ${currentSite?.Name}`}
                                                disabled={isImported || !state.Active}
                                            />
                                        )}

                                    {currentSite?.ID &&
                                        !!state.Settings?.isDistrictPage &&
                                        state.Workspace !== 'live' && (
                                            <Alert severity={'info'} sx={{ marginBottom: 1 }}>
                                                Distributed page content can only be edited in the live workspace.
                                            </Alert>
                                        )}

                                    <AppAccordion
                                        expanded={accordionExpanded[defaultSEOAccordionId]}
                                        onChangeHandler={(expanded) =>
                                            setAccordionExpanded({
                                                ...accordionExpanded,
                                                [defaultSEOAccordionId]: expanded
                                            })
                                        }
                                        allOnChangeHandler={(expanded) => {
                                            setAllAccordions(expanded)
                                        }}
                                        summary={
                                            <Typography component={'p'} variant='h5'>
                                                SEO
                                            </Typography>
                                        }
                                        details={
                                            <SeoOptions
                                                noWrapper
                                                value={{
                                                    mediaId: state.MediaID,
                                                    seoTitle: state.Settings?.seoTitle || '',
                                                    seoDescription: state.Settings?.seoDescription || ''
                                                }}
                                                onChange={({ mediaId, seoTitle, seoDescription }) => {
                                                    setState(
                                                        (prev) =>
                                                            prev && {
                                                                ...prev,
                                                                MediaID: mediaId,
                                                                Settings: {
                                                                    ...prev?.Settings,
                                                                    seoTitle,
                                                                    seoDescription
                                                                }
                                                            }
                                                    )
                                                }}
                                                disabled={!hasPermission || !isEditMode || !state.Active}
                                            />
                                        }
                                    />
                                    <AppAccordion
                                        expanded={accordionExpanded[defaultMetaAccordionId]}
                                        onChangeHandler={(expanded) =>
                                            setAccordionExpanded({
                                                ...accordionExpanded,
                                                [defaultMetaAccordionId]: expanded
                                            })
                                        }
                                        allOnChangeHandler={(expanded) => {
                                            setAllAccordions(expanded)
                                        }}
                                        summary={
                                            <Typography component={'div'} variant='h5'>
                                                Meta
                                            </Typography>
                                        }
                                        details={
                                            <MetaEditor
                                                value={state.Meta || {}}
                                                onChange={(v) => {
                                                    setState({ ...state, Meta: v })
                                                }}
                                                disabled={!hasPermission || !isEditMode || !state.Active}
                                            />
                                        }
                                    />
                                </Grid>
                            </Grid>

                            {!isMinimized && (
                                <Grid container item xs={4} data-testid='content-editor-left-column'>
                                    <Grid item xs={12}>
                                        {isImported && (
                                            <EditableFieldsList hasPermission={hasPermission} importInfo={importInfo} />
                                        )}

                                        <Card sx={{ p: 2 }}>
                                            {!hasPermission && (
                                                <Alert severity={'info'} sx={{ marginBottom: 1 }}>
                                                    {hasDistributedPagePermission && state?.Settings?.isDistrictPage
                                                        ? `You are using a Distributed Page. A portion of this page is locked. Please scroll down to add or edit your content.`
                                                        : 'You dont have access to this page - this is a read-only view.'}
                                                </Alert>
                                            )}

                                            <FormControl fullWidth sx={{ my: 1 }}>
                                                <div
                                                    style={{
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        marginBottom: '20px',
                                                        marginTop: '-10px'
                                                    }}
                                                >
                                                    <FormLabel
                                                        id='publish-period'
                                                        style={{ textTransform: 'capitalize' }}
                                                    >
                                                        {state.Type} Status:{' '}
                                                    </FormLabel>
                                                    <FormLabel id='publish-status'>
                                                        <PublishStatusLabel
                                                            publishAt={state.PublishAt}
                                                            expireAt={state.ExpireAt}
                                                        />
                                                    </FormLabel>
                                                </div>

                                                <TextField
                                                    label='Title'
                                                    required
                                                    style={{ width: '100%' }}
                                                    value={state.Title || ''}
                                                    onChange={(v) => {
                                                        state && setState({ ...state, Title: v.target.value })
                                                    }}
                                                    disabled={isInputDisabled('title') || isFieldRestricted('Title')}
                                                    error={!!errors.Title}
                                                />
                                            </FormControl>
                                            {state.Type !== 'alert' && (
                                                <FormControl fullWidth sx={{ my: 1 }}>
                                                    <RouteEditor
                                                        value={state.Route}
                                                        onChange={(v) => {
                                                            state && setState({ ...state, Route: v })
                                                        }}
                                                        contentType={state.Type as ContentType}
                                                        disabled={
                                                            isInputDisabled('route') || isFieldRestricted('Route')
                                                        }
                                                        error={errors.Route}
                                                    />
                                                </FormControl>
                                            )}

                                            {state.Type === 'news' && (
                                                <PinNews
                                                    value={
                                                        Number.isFinite(state.Settings?.priority)
                                                            ? state.Settings.priority
                                                            : null
                                                    }
                                                    onChange={(v) =>
                                                        setState({
                                                            ...state,
                                                            Settings: { ...state.Settings, priority: v }
                                                        })
                                                    }
                                                    disabled={!hasPermission || !isEditMode}
                                                />
                                            )}
                                        </Card>

                                        {state.Type === 'event' && (
                                            <Card sx={{ my: 1, p: 2 }}>
                                                <EventDateTimePickerV2
                                                    value={state.Settings}
                                                    onChange={(v) => {
                                                        setState({
                                                            ...state,
                                                            Settings: {
                                                                ...(state?.Settings || {}),
                                                                startdate: v.startdate,
                                                                enddate: v.enddate,
                                                                isAllDay: v.isAllDay,
                                                                rrule: v.rrule,
                                                                exdate: v.exdate,
                                                                rdate: v.rdate
                                                            }
                                                        })
                                                    }}
                                                    disabled={Boolean(!hasPermission || isImported || !isEditMode)}
                                                    error={errors.Settings} // TODO: add error message for event date
                                                />
                                                <div style={{ marginTop: '12px' }}>
                                                    <TextField
                                                        label={'Location'}
                                                        placeholder={'Where will this event take place?'}
                                                        value={state.Settings?.location?.displayName || ''}
                                                        onChange={(v) => {
                                                            setState({
                                                                ...state,
                                                                Settings: {
                                                                    ...(state?.Settings || {}),
                                                                    location: {
                                                                        displayName: v?.target?.value || ''
                                                                    }
                                                                }
                                                            })
                                                        }}
                                                        disabled={Boolean(!hasPermission || isImported || !isEditMode)}
                                                    />
                                                </div>
                                            </Card>
                                        )}

                                        <Card sx={{ my: 1, p: 2 }}>
                                            <BaseForm
                                                value={{
                                                    PublishAt: state.PublishAt,
                                                    ExpireAt: state.ExpireAt,
                                                    PrivacyLevel: state.PrivacyLevel,
                                                    Sites: state.Sites,
                                                    DepartmentID: state.DepartmentID
                                                }}
                                                hiddenFields={{ PublishStatusLabel: true }}
                                                onChange={(b) => {
                                                    setState({
                                                        ...state,
                                                        PublishAt: b.PublishAt,
                                                        ExpireAt: b.ExpireAt,
                                                        PrivacyLevel: b.PrivacyLevel,
                                                        Sites: b.Sites,
                                                        DepartmentID: b.DepartmentID
                                                    })
                                                }}
                                                contentType={asSecured(state).EntityScope}
                                                disabledFields={{
                                                    Sites: isInputDisabled('site') || isFieldRestricted('Sites'),
                                                    DepartmentID:
                                                        isInputDisabled('departmentId') || isFieldRestricted('Sites'),
                                                    PrivacyLevel:
                                                        isInputDisabled('privacyLevel') ||
                                                        isFieldRestricted('PrivacyLevel'),
                                                    PublishAt:
                                                        isInputDisabled('publishAt') || isFieldRestricted('Scheduling'),
                                                    ExpireAt:
                                                        isInputDisabled('expireAt') || isFieldRestricted('Scheduling')
                                                }}
                                                errors={errors}
                                            />
                                        </Card>

                                        <Card sx={{ my: 1, p: 2 }}>
                                            <FormControl fullWidth sx={{ my: 1 }}>
                                                <TemplateSelector
                                                    ownerID={state.ID}
                                                    path={state.Path}
                                                    onChange={(path, tpl) => {
                                                        setState({ ...state, Path: path })
                                                        setSelectedTemplate(tpl)
                                                    }}
                                                    onLoaded={(tpl) => setSelectedTemplate(tpl)}
                                                    templateType={'all'}
                                                    classifications={classifications}
                                                    error={errors.Path}
                                                    disabled={isInputDisabled('path') || isFieldRestricted('Template')}
                                                    allowedTemplateIDs={cdi?.Templates || undefined}
                                                />
                                            </FormControl>
                                            <FormControl fullWidth sx={{ my: 1 }}>
                                                <StructureSelector
                                                    required
                                                    value={state.StructureID}
                                                    onChange={(v, s) => {
                                                        setState({ ...state, StructureID: v || null })
                                                        setStructure(s?.FormStructure)
                                                    }}
                                                    selectedStructure={(s) =>
                                                        !structure && setStructure(s.FormStructure)
                                                    }
                                                    disabled={
                                                        isInputDisabled('structure') || isFieldRestricted('Template')
                                                    }
                                                    error={errors.StructureID}
                                                    allowedStructures={allowedStructures}
                                                />
                                            </FormControl>
                                        </Card>

                                        <Card sx={{ my: 1, p: 2 }}>
                                            <FormControl fullWidth sx={{ my: 1 }}>
                                                <TagsSelector
                                                    selected={state.Tags || []}
                                                    disabled={isInputDisabled('tags')}
                                                    tagTypes={[state.Type]}
                                                    onChange={(tags) => {
                                                        tags && setState({ ...state, Tags: tags })
                                                    }}
                                                    hasError={!!errors.Tags}
                                                    allowedTags={cdi?.Tags || undefined}
                                                    min={cdi?.TagsMinMax?.[0]}
                                                    max={cdi?.TagsMinMax?.[1]}
                                                />
                                            </FormControl>
                                        </Card>

                                        {state.Type !== 'alert' &&
                                            (!!state.Settings?.isDistrictPage || state.Sites.length > 1) && (
                                                <Card sx={{ my: 1, p: 2 }}>
                                                    <FormControlLabel
                                                        control={
                                                            <Checkbox
                                                                onChange={(e) => {
                                                                    state &&
                                                                        setState({
                                                                            ...state,
                                                                            Settings: {
                                                                                ...(state.Settings || {}),
                                                                                isDistrictPage: e.target.checked
                                                                            }
                                                                        })
                                                                }}
                                                                checked={!!state.Settings?.isDistrictPage}
                                                                name='first'
                                                            />
                                                        }
                                                        label='Allow additional site specific content on shared sites'
                                                        disabled={Boolean(
                                                            !hasPermission || isImported || !isEditMode || !state.Active
                                                        )}
                                                    />
                                                </Card>
                                            )}

                                        {state.Type === 'page' && (
                                            <Card sx={{ my: 1, p: 2 }}>
                                                <Alert severity={'info'} style={{ marginBottom: '0.5rem' }}>
                                                    Navigation changes for {currentSite?.Name} can be done in the
                                                    Navigation Editor
                                                </Alert>
                                                <GoToNavigation id={state.ID} active={state.Active} />
                                            </Card>
                                        )}

                                        {state.Type !== 'alert' && state && (
                                            <Card sx={{ my: 1, p: 2 }}>
                                                <LegacyUrls
                                                    contentId={state.ID}
                                                    hasPermission={hasPermission && state.Active}
                                                />
                                            </Card>
                                        )}

                                        {state.Type === 'alert' && (
                                            <AlertNotification
                                                state={state}
                                                onChange={(e) => {
                                                    setState({
                                                        ...state,
                                                        PrivacyLevel: e.target.checked ? 0 : state.PrivacyLevel,
                                                        Settings: {
                                                            ...(state.Settings || {}),
                                                            HasEmailNotification: e.target.checked
                                                        }
                                                    })
                                                }}
                                                hasPermission={hasPermission && state.Active}
                                                notificationErrors={notificationErrors}
                                                onChange1={(e) => {
                                                    setState({
                                                        ...state,
                                                        Settings: {
                                                            ...(state.Settings || {}),
                                                            EmailSubject: e.target.value
                                                        }
                                                    })
                                                }}
                                                onChange2={(e) => {
                                                    setState({
                                                        ...state,
                                                        Settings: {
                                                            ...(state.Settings || {}),
                                                            EmailBody: e.target.value
                                                        }
                                                    })
                                                }}
                                            />
                                        )}

                                        {state.Active && (
                                            <AppAccordion
                                                unmountOnExit
                                                defaultExpanded={false}
                                                withoutPadding
                                                summary={'Lists'}
                                                details={
                                                    <Box pt='12px'>
                                                        {canUpdateDistributed && (
                                                            <>
                                                                <Typography
                                                                    component={'p'}
                                                                    sx={{ fontWeight: 'bold', marginLeft: '17px' }}
                                                                >
                                                                    Distributed
                                                                </Typography>
                                                                <DistributedListsForContent
                                                                    ContentID={state.ID}
                                                                    ContentType={state.Type}
                                                                />
                                                            </>
                                                        )}

                                                        <Typography
                                                            component={'p'}
                                                            sx={{ fontWeight: 'bold', marginLeft: '17px' }}
                                                        >
                                                            Individual
                                                        </Typography>
                                                        <ListsForContent
                                                            ContentID={state.ID}
                                                            ContentType={state.Type}
                                                        />
                                                    </Box>
                                                }
                                            />
                                        )}

                                        {(state.Type === 'page' || state.Type === 'news' || state.Type === 'event') && (
                                            <SearchTools
                                                contentID={state.ID}
                                                value={state.Settings?.ContentIndexingConfig}
                                                onChange={(config: ContentIndexingConfig) => {
                                                    setState({
                                                        ...state,
                                                        Settings: {
                                                            ...state.Settings,
                                                            ContentIndexingConfig: config
                                                        }
                                                    })
                                                }}
                                                isDistributed={!!state.Settings?.isDistrictPage}
                                                disabled={isInputDisabled('contentIndexingConfig')}
                                            />
                                        )}

                                        <AppAccordion
                                            unmountOnExit
                                            defaultExpanded={false}
                                            withoutPadding
                                            summary={'Revision History'}
                                            details={
                                                <RevisionHistoryList
                                                    disabled={!isEditMode || !state.Active}
                                                    content={state}
                                                    onRevertHandler={(newValue) => {
                                                        setState({ ...newValue })
                                                        setContentEditorIsLoading(true)
                                                    }}
                                                />
                                            }
                                        />

                                        {state?.Workspace === 'live' && state?.Active && (
                                            <Button
                                                onClick={async () => {
                                                    if (
                                                        window.confirm(
                                                            `Are you sure you want to delete this ${state.Type}?`
                                                        )
                                                    ) {
                                                        await deleteContentQuery(state.ID, state.Workspace)
                                                        await fetcher.refetch()
                                                    }
                                                }}
                                                variant='outlined'
                                                color='error'
                                                style={{ display: 'block', width: '100%', marginBottom: '1vh' }}
                                                disabled={!hasPermission}
                                            >
                                                Delete this {state.Type}
                                            </Button>
                                        )}
                                    </Grid>
                                </Grid>
                            )}
                        </Grid>
                    )}
                </PageContainer>
            )}
        </Box>
    )
}
