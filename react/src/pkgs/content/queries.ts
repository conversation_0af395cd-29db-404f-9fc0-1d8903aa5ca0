import { useMutation, useQuery } from '@tanstack/react-query'
import { httpDelete, httpGet, httpPatch, httpPost, httpPut } from '../../common/client'
import { Content, content, contentResult, ContentType } from './types'
import { createBatcher, keyResolver, windowScheduler } from '../../helpers/batcher/batcher'
import { z } from 'zod'
import { notify } from '../../helpers'
import { guessErrorMessage } from '../../helpers/guessErrorMessage'
import { baseQueryConfig, cancelOn401, PagingQuery, SortingQuery } from '../../common/react-query'
import { publishStatus } from './editor/ContentEditorSaveBar'
import { BASE, ContentAPIV2 } from '../../common/constants'
import { failure, success } from '@/helpers/result'

export const useContentDetailsQuery = (id: string, workspace: string) => {
    const query = useQuery({
        ...baseQueryConfig,
        queryKey: ['content-details-query', id, workspace],
        queryFn: async () => httpGet(`${BASE}/api/v2/content/${id}/${workspace}`, {}, content)
    })

    return query
}

export const useBatchedContentDetails = (id: string | undefined | null) =>
    useQuery({
        ...baseQueryConfig,
        queryKey: ['content-details-query', id],
        enabled: !!id,
        queryFn: async () => {
            if (id) {
                const result = await contentDetailsBatched.fetch(id)
                if (result) {
                    return result
                }
            }
            const notFound: Content = {
                Path: '',
                PrivacyLevel: 0,
                ID: id || '',
                Workspace: '',
                Title: 'Not Found',
                Active: false,
                Owner: '',
                Publisher: '',
                Created: new Date(),
                Updated: new Date(),
                Type: '',
                Route: '',
                Sites: [],
                PageLayout: ''
            }
            return notFound
        }
    })

const contentDetailsBatched = createBatcher<Content[], string, Content>({
    fetcher: async (ids: string[]) => {
        const result = await httpGet(ContentAPIV2, { ids: ids, pageSize: 1000 }, contentResult)
        return result.Rows
    },
    resolver: keyResolver('ID'),
    scheduler: windowScheduler(100)
})

export interface ContentQueryParams extends PagingQuery, SortingQuery {
    Search?: string
    ContentTypes?: ContentType[]
    Tags?: string[] // ids
    Departments?: string[]
    Sites?: string[]
    // StructureID(s) ??
    StructureID?: string | null
    Status?: publishStatus | ''
    Inactive?: boolean
    SiteOnly?: boolean
    Editors?: string[]
    Templates?: string[]
    UsePriority?: boolean
}

export function useContentQuery(q: ContentQueryParams) {
    const query = useQuery({
        ...baseQueryConfig,
        queryKey: ['content-query-v2', q],
        queryFn: async () => httpGet(ContentAPIV2, q, contentResult)
    })

    return query
}

export function updateContentQuery(content: Content) {
    return httpPut(`${ContentAPIV2}/${content.ID}/${content.Workspace}`, content)
}

export function deleteContentQuery(id: string, workspace: string) {
    return httpDelete(`${ContentAPIV2}/${id}/${workspace}`)
}

export function restoreContentQuery(id: string) {
    // only live can be restored (when `live` content is deleted all other workspaces are deleted as well)
    return httpPatch(`${ContentAPIV2}/${id}/live/restore`, null)
}

export function hideNowContentQuery(id: string, workspace: string) {
    return httpPatch(`${ContentAPIV2}/${id}/${workspace}/expire`, null)
}

export function cloneContentQuery(id: string, workspace: string) {
    return httpPost(`${ContentAPIV2}/${id}/${workspace}/clone`, null).then(
        // @ts-ignore
        (res) => success(res?.data || res),
        (err) => failure(err)
    )
}

export const useContentQueries = (params: { id: string | undefined; workspace: string | undefined }) => {
    const { id, workspace } = params
    const fetcher = useQuery({
        refetchOnWindowFocus: false,
        keepPreviousData: false,
        retry: cancelOn401,
        cacheTime: 0,
        enabled: !!id && !!workspace,
        queryKey: ['content-query', params],
        queryFn: async () => httpGet(`${BASE}/api/v2/content/${id}/${workspace}`, {}, content)
    })

    const updater = useMutation({
        mutationFn: updateContentQuery,
        onSuccess: (data, variables, context) => {
            fetcher.refetch()
            notify(`Success! ${variables?.Title} updated`, 'info')
        },
        onError: (err: any, variables) => {
            const errorMessage = guessErrorMessage(err)
            notify(`Oops! ${variables?.Title} could not be updated: ${errorMessage}`, 'error')
        }
    })

    const autoSaveUpdater = useMutation({
        mutationFn: updateContentQuery,
        onSuccess: (data, variables, context) => {
            fetcher.refetch()
        },
        onError: (err: any, variables) => {
            const errorMessage = guessErrorMessage(err)
            notify(`Oops! ${variables?.Title} could not be updated: ${errorMessage}`, 'error')
        }
    })

    const brancher = useMutation({
        mutationFn: (content: Content) => httpPost(`${ContentAPIV2}`, content),
        onSuccess: (data, variables, context) => {
            fetcher.refetch()
            notify(`Success! The ${variables.Workspace} version of ${variables?.Title} created`, 'info')
        },
        onError: (err: any, variables) => {
            const errorMessage = guessErrorMessage(err)
            notify(`Oops! ${variables?.Title} could not be updated: ${errorMessage}`, 'error')
        }
    })

    return { fetcher, updater, brancher, autoSaveUpdater }
}

export function getDeleteContentMutation() {
    return useMutation({
        mutationFn: ({ content, _then }: { content: Content; _then?: () => void }) =>
            deleteContentQuery(content.ID, content.Workspace),
        onSuccess: (data, variables, context) => {
            notify(`Success! ${variables?.content.Title} deleted`, 'info')
            variables?._then?.()
        },
        onError: (err: any, variables) => {
            const msg = guessErrorMessage(err)
            notify(`Oops! ${variables?.content.Title} could not be deleted: ${msg}`, 'error')
            const errorMessage = err?.response?.data?.ErrorMessage
            console.log('update content error', errorMessage)
        }
    })
}
