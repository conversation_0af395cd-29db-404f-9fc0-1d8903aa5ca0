import { BASE } from '@/common/constants'
import { Alert, <PERSON>ton, DialogActions, DialogContent, FormControl, MenuItem, Select } from '@mui/material'
import { z } from 'zod'
import { useQuery } from '@tanstack/react-query'
import { httpGet } from '@/common/client'
import { LoadingSpinner } from '@/common/components/CenteredSpinner'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { useEffect, useState } from 'react'
import CMDialog from '@/common/components/CMDialog'

type WorkspaceSelectorProps = {
    entityID: string
    tableName: string
    value: string
    onChange: (v: string) => void
    onDisabledWorkspacesChange?: (disabledWorkspaces: string[]) => void
    hasChanges?: boolean
}

export const WorkspaceSelector = ({
    value,
    onChange,
    entityID,
    tableName,
    onDisabledWorkspacesChange,
    hasChanges = false
}: WorkspaceSelectorProps) => {
    const workspacesResult = useQuery({
        queryKey: ['workspaces'],
        refetchOnWindowFocus: false,
        keepPreviousData: true,
        cacheTime: Infinity,
        refetchOnMount: false,
        queryFn: async () => {
            return httpGet(`${BASE}/api/v1/workspaces`, null, z.array(z.string()))
        }
    })

    const [disabledWorkspaces, setDisabledWorkspaces] = useState<string[]>([])

    const [confirmDialogIsOpen, setConfirmDialogIsOpen] = useState(false)
    const [selectedWorkspace, setSelectedWorkspace] = useState<string>(value)

    const availableWorkspaces = workspacesResult.data || []

    // We can "clone" only live workspace (we may reconsider it later)
    const result = useQuery({
        queryKey: ['workspaces', entityID, tableName],
        enabled: !!entityID && !!tableName,
        refetchOnWindowFocus: false,
        queryFn: async () => {
            return httpGet(`${BASE}/api/v1/workspaces/${tableName}/${entityID}`, null, z.array(z.string()))
        }
    })

    useEffect(() => {
        if (!result.data) return

        const disabled = result.data.includes('live') ? [] : availableWorkspaces.filter((w) => !result.data.includes(w))
        setDisabledWorkspaces(disabled)
        onDisabledWorkspacesChange?.(disabled)
    }, [result.data, onDisabledWorkspacesChange])

    if (result.isLoading) {
        return <LoadingSpinner />
    }

    if (result.isError) {
        return <Alert severity='error'>Error loading workspaces: {guessErrorMessage(result.error)}</Alert>
    }

    return (
        <FormControl sx={{ width: '90px' }}>
            <Select
                size='small'
                value={value}
                onChange={(v) => {
                    if (hasChanges) {
                        setSelectedWorkspace(v.target.value as string)
                        setConfirmDialogIsOpen(true)
                        return
                    }

                    onChange(v.target.value as string)
                }}
            >
                {availableWorkspaces.map((o) => (
                    <MenuItem key={o} value={o} disabled={disabledWorkspaces.includes(o)}>
                        {o}
                    </MenuItem>
                ))}
            </Select>

            {confirmDialogIsOpen && (
                <CMDialog
                    open={confirmDialogIsOpen}
                    onClose={() => setConfirmDialogIsOpen(false)}
                    title={'Unsaved changes'}
                    showCloseButton
                >
                    <DialogContent>
                        <Alert severity={'warning'}>
                            You have unsaved changes. Are you sure you want to change the workspace?
                        </Alert>
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={() => setConfirmDialogIsOpen(false)}>Cancel</Button>
                        <Button
                            variant={'contained'}
                            color={'warning'}
                            size={'small'}
                            onClick={() => {
                                onChange(selectedWorkspace)
                                setConfirmDialogIsOpen(false)
                            }}
                        >
                            Discard changes and switch to {selectedWorkspace}
                        </Button>
                    </DialogActions>
                </CMDialog>
            )}
        </FormControl>
    )
}
