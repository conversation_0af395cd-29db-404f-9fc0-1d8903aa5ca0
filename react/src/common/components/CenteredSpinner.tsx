import { Box, CircularProgress } from '@mui/material'
import { colours } from '../colours'

interface CenteredSpinnerProps {
    size?: number
}

function CenteredSpinner({ size = 40 }: CenteredSpinnerProps) {
    return (
        <Box width='100%' justifyContent='center' textAlign='center'>
            <svg width={0} height={0}>
                <defs>
                    <linearGradient id='my_gradient' x1='0%' y1='0%' x2='0%' y2='100%'>
                        <stop offset='0%' stopColor='#e01cd5' />
                        <stop offset='70%' stopColor={colours.topbar} />
                        <stop offset='100%' stopColor={colours.orange} />
                    </linearGradient>
                </defs>
            </svg>
            <CircularProgress size={size} sx={{ 'svg circle': { stroke: 'url(#my_gradient)' } }} />
        </Box>
    )
}
export const LoadingSpinner = CenteredSpinner

export default CenteredSpinner
