import React, { useEffect, useState } from 'react'
import { useAppContext } from '@/pkgs/auth/atoms'
import { is } from '@/helpers'

export const storage = {
    setItem<T>(key: string, value: T) {
        const v = JSON.stringify(value)
        sessionStorage.setItem(key, v)
        localStorage.setItem(key, v)
    },
    getItem<T>(key: string): T | null {
        return JSON.parse(sessionStorage.getItem(key) || localStorage.getItem(key) || 'null') as T
    }
}

export function useTenantStorageKey(key: string) {
    const appContext = useAppContext()
    const tenantKey = `${appContext.tenantID}-${key}`
    return tenantKey
}

// "ignore" will ignore stored value. Hooks cannot be used conditionally so this helps conditionally force defaultValue
export const useStateWithStorage = <T>(
    key: string,
    defaultValue: T,
    ignore?: boolean
): [T, React.Dispatch<React.SetStateAction<T>>] => {
    const tenantKey = useTenantStorageKey(key)

    // only if both default value and storage value are objects { ...defaultValue, ...storageValue }
    const storedValue = storage.getItem<T>(tenantKey)

    let initialValue = typeof storedValue == typeof defaultValue ? storedValue || defaultValue : defaultValue
    if (typeof storedValue === 'boolean' && typeof defaultValue === 'boolean') {
        initialValue = storedValue
    }

    // to help with: typeof [] === 'object' == true
    initialValue = is.array(initialValue) !== is.array(defaultValue) ? defaultValue : initialValue

    if (is.object(defaultValue) && is.object(storedValue)) {
        initialValue = { ...initialValue, ...storedValue }
    }

    const [value, setValue] = useState<T>(ignore ? defaultValue : initialValue)

    useEffect(() => {
        const shouldOmitData =
            typeof value === 'object' &&
            value !== null &&
            hasDataProperty(value) &&
            Array.isArray(value.data) &&
            'currentPage' in value // sanity check for paginationState

        const newValue = shouldOmitData ? omitData(value) : value

        if (!ignore) {
            try {
                storage.setItem(tenantKey, newValue)
            } catch (e) {
                console.error('Failed to store state in local storage', e)
            }
        }
    }, [key, value])

    return [value, setValue]
}
function omitData(state) {
    const { data, ...rest } = state
    return { ...rest, data: [] }
}

function hasDataProperty(value: any): value is { data: any } {
    return typeof value === 'object' && value !== null && 'data' in value
}
