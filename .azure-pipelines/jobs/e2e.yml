jobs:
  - job: e2e_tests
    displayName: 'E2E Tests'
    pool:
      vmImage: 'ubuntu-latest'
    variables:
      NODE_OPTIONS: --max_old_space_size=8192
      DOCKER_BUILDKIT: 1
      BUILDKIT_INLINE_CACHE: 1

    steps:
      - template: ../templates/setup.yml
      - template: ../templates/setup-node.yml
        parameters:
          enablePlaywrightCache: true

      - script: |
          docker buildx create --use --name mybuilder --driver docker-container
        displayName: 'Create Docker BuildKit builder'

      - script: |
          docker buildx build \
              --build-arg BUILDKIT_INLINE_CACHE=1 \
              --cache-from type=local,src=/tmp/.buildx-cache \
              --cache-to type=local,dest=/tmp/.buildx-cache,mode=max \
              --load \
              --target migrator \
              -t e2e-migrator:latest -f e2e/Dockerfile-migrator .
        displayName: 'Build migrator'

      - script: |
          docker compose -p e2e -f ./e2e/docker-compose.e2e.yml up -d
        displayName: 'Start docker-compose'

      - script: |
          cd e2e
          npm ci
        condition: and(succeeded(), ne(variables.E2E_CACHE_RESTORED, 'true'))
        displayName: 'Install React dependencies'

      - script: |
          cd e2e
          npx playwright install --with-deps
        displayName: 'Install Playwright tests'

      - script: |
          echo "127.0.0.1 contentmanager.imagineeverything.ca.localhost" | sudo tee -a /etc/hosts
          
          # Not required, but useful for explicit testing
          echo "127.0.0.1 site-1.tenant-1.localhost" | sudo tee -a /etc/hosts
          echo "127.0.0.1 site-2.tenant-1.localhost" | sudo tee -a /etc/hosts
          echo "127.0.0.1 site-3.tenant-1.localhost" | sudo tee -a /etc/hosts
          echo "127.0.0.1 site-1.tenant-2.localhost" | sudo tee -a /etc/hosts
        displayName: 'Update /etc/hosts for custom localhost domains'

      - script: |
          # Wait for the cm-api service to be ready
          timeout 180s bash -c 'until curl -s -f -k https://contentmanager.imagineeverything.ca.localhost/api/v1/status; do sleep 2; done'
        displayName: 'Wait for services to be ready'

      - script: |
          echo "##vso[task.logissue type=warning]Logs from API (Before)"
          
          docker compose -p e2e -f ./e2e/docker-compose.e2e.yml logs cm-api
        displayName: 'Logs from API (Before)'

      - script: |
          cd e2e
          npx playwright test
        env:
          CI: 'true'
        displayName: 'Run Playwright tests'

      - script: |
          echo "##vso[task.logissue type=warning]Logs from DB"
          docker compose -p e2e -f ./e2e/docker-compose.e2e.yml logs postgres
        condition: always()
        displayName: 'Logs from DB'

      - script: |
          echo "##vso[task.logissue type=warning]Logs from Migrator"
          docker compose -p e2e -f ./e2e/docker-compose.e2e.yml logs migrator
        condition: always()
        displayName: 'Logs from Migrator'

      - script: |
          echo "##vso[task.logissue type=warning]Logs from API (After)"
          docker compose -p e2e -f ./e2e/docker-compose.e2e.yml logs cm-api
        condition: always()
        displayName: 'Logs from API (After)'

      - publish: $(System.DefaultWorkingDirectory)/e2e/playwright-report
        artifact: playwright-results
        displayName: 'Upload Playwright test results'
        condition: always()

      - task: Bash@3
        inputs:
          filePath: '$(Build.SourcesDirectory)/.azure-pipelines/scripts/azcopy-report.sh'
        displayName: 'Upload Playwright test results to Azure Blob Storage'
        env:
          AZCOPY_AUTO_LOGIN_TYPE: SPN
          AZCOPY_SPA_APPLICATION_ID: $(AZCOPY_SPA_APPLICATION_ID)
          AZCOPY_SPA_CLIENT_SECRET: $(AZCOPY_SPA_CLIENT_SECRET)
          AZCOPY_TENANT_ID: $(AZCOPY_TENANT_ID)
        condition: always()

      - script: |
          docker compose -p e2e -f ./e2e/docker-compose.e2e.yml down
        displayName: 'Tear down services'
        condition: always()
