jobs:
  - job: build_go
    displayName: 'Build Go'
    pool:
      vmImage: 'ubuntu-latest'
    variables:
      GO111MODULE: on
      CGO_ENABLED: 0
      GOOS: linux
      GOARCH: amd64

    steps:
      - template: ../templates/setup.yml
        parameters:
          repositoryToCheck: 'cm-api'

      - template: ../templates/setup-go.yml
        parameters:
          enableCache: true

      - script: |
          git config --global url."https://$(IE_GIT_PAT)@github.com".insteadOf "https://github.com"
        displayName: 'Configure git for private modules'

      - script: |
          cd go
          go mod download
        condition: ne(variables.GO_MOD_CACHE_RESTORED, 'true')
        displayName: 'Download Go dependencies'

      - script: |
          cd go
          go test ./...
        displayName: 'Run Go tests'

      - template: ../templates/bash-script-template.yml
        parameters:
          scriptName: 'build-go.sh'

      - template: ../templates/list-output.yml
