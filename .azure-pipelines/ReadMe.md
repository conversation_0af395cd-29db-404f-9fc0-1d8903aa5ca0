The main file processed by the Azure pipeline is `ci.yml`.

## Secrets

- Env Variables of pipeline: https://dev.azure.com/imagineeverything/Content%20Manager/_library?itemType=VariableGroups&view=VariableGroupView&variableGroupId=1&path=contentmanager
- E2E results uploads secrets: https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/Credentials/appId/5945a7db-0e4a-44e4-8c0d-24d66de1f027


The `jobs` and `templates` directories contain reusable YAML fragments:

- **`jobs`**: Contains job definitions.
- **`templates`**: Contains templates that can be included in multiple places.

Both directories are used to modularize and reuse code within the pipeline.

The hierarchy is:

```
ci.yml -> jobs -> templates
```

The `scripts` directory contains Bash scripts that are referenced in the YAML files but are too extensive to include inline.
