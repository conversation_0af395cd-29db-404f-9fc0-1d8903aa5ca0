#!/bin/bash
set -euo pipefail

if aws ecr describe-images --repository-name "$REPOSITORY" --image-ids imageTag="$COMMIT_SHA" --region "$AWS_REGION" &> /dev/null; then
  echo "##vso[task.logissue type=error]Image in $REPOSITORY for commit $COMMIT_SHA already exists. Attempting to cancel pipeline."
  echo "Image for commit $COMMIT_SHA already exists. Attempting to cancel pipeline."

  response=$(curl -s -w "\n%{http_code}" -X PATCH \
    -H "Content-Type: application/json" \
    -H "Authorization: Basic $(echo -n ":$ACCESS_TOKEN" | base64)" \
    "https://dev.azure.com/imagineeverything/Content%20Manager/_apis/build/builds/$BUILD_BUILDID?ignoreWarnings={ignoreWarnings}&checkInTicket={checkInTicket}&sourceBuildId={sourceBuildId}&definitionId={definitionId}&api-version=6.0" \
    -d '{"status":"Cancelling"}')

  body=$(echo "$response" | sed -e '$d')
  status_code=$(echo "$response" | tail -n1)

  echo "Response body: $body"
  echo "Status code: $status_code"

  if [ "$status_code" -eq 200 ]; then
    echo "Successfully requested pipeline cancellation."
    echo "##vso[task.complete result=Canceled;]DONE"
    exit 0
  else
    echo "Failed to cancel pipeline. HTTP status code: $status_code"
    echo "Response body: $body"
    echo "##vso[task.logissue type=error]Failed to cancel pipeline"
    exit 1
  fi
else
  echo "Image in $REPOSITORY for commit $COMMIT_SHA does not exist. Proceeding with build."
fi
