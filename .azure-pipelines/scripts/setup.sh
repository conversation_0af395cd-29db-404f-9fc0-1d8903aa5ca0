#!/bin/bash
set -euo pipefail

: "${BUILD_SOURCESDIRECTORY:?BUILD_SOURCESDIRECTORY must be set}"
: "${BUILD_SOURCEVERSION:?BUILD_SOURCEVERSION is not set}"
: "${BUILD_REASON:?BUILD_REASON is not set}"
: "${BUILD_SOURCEBRANCH:?BUILD_SOURCEBRANCH is not set}"
: "${BUILD_BUILDNUMBER:?BUILD_BUILDNUMBER is not set}"

COMMIT_SHA=$BUILD_SOURCEVERSION

if [ "$BUILD_REASON" = "PullRequest" ]; then
  BRANCH_NAME=$SYSTEM_PULLREQUEST_SOURCEBRANCH
  # Remove 'refs/pull/' and '/merge' from branch name
  BRANCH_NAME=${BRANCH_NAME#refs/pull/}
  BRANCH_NAME=${BRANCH_NAME%/merge}
else
  BRANCH_NAME=$BUILD_SOURCEBRANCH
  # Remove 'refs/heads/' from branch name
  BRANCH_NAME=${BRANCH_NAME#refs/heads/}
fi

# Replace '/' with '-' in branch name for Docker tag compatibility
BRANCH_NAME=${BRANCH_NAME//\//-}

BRANCH_NAME_WITH_NUMBER="${BRANCH_NAME}__$BUILD_BUILDNUMBER"

echo "BUILD_BUILDNUMBER: $BUILD_BUILDNUMBER"
echo "COMMIT_SHA: $COMMIT_SHA"
echo "BRANCH_NAME_WITH_NUMBER: $BRANCH_NAME_WITH_NUMBER"

# Define the output directory
OUTPUT_DIR="$BUILD_SOURCESDIRECTORY/build_output"
mkdir -p "$OUTPUT_DIR"

echo -e "$COMMIT_SHA,$BUILD_BUILDNUMBER,$BRANCH_NAME_WITH_NUMBER" > "$OUTPUT_DIR/image_tags.txt"

echo "##vso[task.setvariable variable=OUTPUT_DIR]$OUTPUT_DIR"
echo "##vso[task.setvariable variable=COMMIT_SHA]$COMMIT_SHA"
echo "##vso[task.setvariable variable=BRANCH_NAME_WITH_NUMBER]$BRANCH_NAME_WITH_NUMBER"

echo "Printing all environment variables:"
printenv | sort
