#!/bin/bash
set -euo pipefail

REPORT_DIR="sha-$COMMIT_SHA"

azcopy cp --recursive "./e2e/playwright-report/*" "https://cme2e.blob.core.windows.net/\$web/$REPORT_DIR/"

# Set the directory you want to tree
DIR_TO_TREE="./e2e/playwright-report"
OUTPUT_FILE="$BUILD_SOURCESDIRECTORY/report.md"
URL_PREFIX="https://cme2e.z9.web.core.windows.net/$REPORT_DIR"

echo "## Produced tags" > "$OUTPUT_FILE"
{
  echo ""
  echo "- COMMIT_SHA: $COMMIT_SHA"
  echo "- BRANCH_NAME_WITH_NUMBER: $BRANCH_NAME_WITH_NUMBER"
} >> "$OUTPUT_FILE"


if [ -d "$DIR_TO_TREE" ]; then
  {
    echo "## E2E Report"
    echo ""
    echo "- **[Test Results]($URL_PREFIX/index.html)**"
  } >> "$OUTPUT_FILE"

  if [ "$AGENT_JOBSTATUS" == "Failed" ]; then
    echo "- [Trace Viewer]($URL_PREFIX/trace/index.html)" >> "$OUTPUT_FILE"
  fi
  echo "### Report links" >> "$OUTPUT_FILE"
  echo "" >> "$OUTPUT_FILE"

  find "$DIR_TO_TREE" -print0 | sort -z | while IFS= read -r -d '' file; do
    relative_path="${file#"$DIR_TO_TREE"/}"
    if [ "$relative_path" = "$DIR_TO_TREE" ]; then
      continue
    fi
    echo "$relative_path"
    depth=$(($(tr -dc '/' <<< "$relative_path" | wc -c)))
    spaces=$(printf '%*s' $((depth * 2)) '')
    name=$(basename "$file")
    if [ -d "$file" ]; then
      echo "${spaces}- **[$name]($URL_PREFIX$relative_path/)**" >> "$OUTPUT_FILE"
    else
      echo "${spaces}- [$name]($URL_PREFIX$relative_path)" >> "$OUTPUT_FILE"
    fi
  done
fi

echo "##vso[task.uploadsummary]$OUTPUT_FILE"
