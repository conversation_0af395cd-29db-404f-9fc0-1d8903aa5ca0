#!/bin/bash
set -euo pipefail

: "${OUTPUT_DIR:?OUTPUT_DIR must be set}"
: "${ECR_URL:?ECR_URL must be set}"

SERVERS=(
  "api"
  "public"
  "media"
  "lookup"
  "mailer"
  "importer"
  "directory-sync"
  "indexer-content"
  "indexer-documents"
)

# Build each server
for server in "${SERVERS[@]}"; do
  echo "Building $server server..."

  # Build golang binary
  cd "$BUILD_SOURCESDIRECTORY/go/servers/$server" || exit 1

  if ! go build -o "$OUTPUT_DIR/$server"; then
    echo "Error building $server server"
    exit 1
  fi
  cd - > /dev/null || exit 1

  TAGS_PARAMS=(
    -t "$ECR_URL/cm-$server:$COMMIT_SHA"
    -t "$ECR_URL/cm-$server:$BRANCH_NAME_WITH_NUMBER"
  )

  # Build & push Docker image
  if ! docker buildx build \
           --build-arg "SERVER_NAME=$server" \
           --target go-server \
           --push \
           "${TAGS_PARAMS[@]}" \
           -f .docker/Dockerfile-prod \
           .; then
    echo "Error Docker building $server server"
    exit 1
  fi
done

echo "All servers built successfully. Binaries located in $OUTPUT_DIR"
