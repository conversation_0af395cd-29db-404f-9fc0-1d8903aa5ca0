parameters:
  - name: enableCache
    type: boolean
    default: false

  - name: goVersion
    type: string
    default: '1.23.x'

steps:
  - task: GoTool@0
    inputs:
      version: '${{ parameters.goVersion }}'

  - script: |
      echo "##vso[task.setvariable variable=GOMODCACHE]$(go env GOMODCACHE)"
      echo "##vso[task.setvariable variable=GOCACHE]$(go env GOCACHE)"
    displayName: 'Set Go cache paths'

  - ${{ if eq(parameters.enableCache, true) }}:
      - task: Cache@2
        inputs:
          key: 'go-mod-v1 | "$(Agent.OS)" | go/go.sum'
          restoreKeys: |
            go-mod-v1 | "$(Agent.OS)"
          path: $(GOMODCACHE)
          cacheHitVar: GO_MOD_CACHE_RESTORED
        displayName: 'Cache Go modules'

      - task: Cache@2
        inputs:
          key: 'go-build-v1 | "$(Agent.OS)" | go/go.sum'
          restoreKeys: |
            go-build-v1 | "$(Agent.OS)"
          path: $(GOCACHE)
        displayName: 'Cache Go build'
