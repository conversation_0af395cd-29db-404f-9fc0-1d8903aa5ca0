parameters:
  - name: enableReactCache
    type: boolean
    default: false
  - name: enablePlaywrightCache
    type: boolean
    default: false

steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '20.x'

  - ${{ if eq(parameters.enableReactCache, true) }}:
      - task: Cache@2
        inputs:
          key: 'npm-v2 | "$(Agent.OS)" | react/package-lock.json'
          restoreKeys: |
            npm | "$(Agent.OS)"
          path: 'react/node_modules'
          cacheHitVar: REACT_CACHE_RESTORED
        displayName: 'Cache npm packages'

  - ${{ if eq(parameters.enablePlaywrightCache, true) }}:
      - task: Cache@2
        inputs:
          key: 'npm-e2e | "$(Agent.OS)" | e2e/package-lock.json'
          restoreKeys: |
            npm-e2e | "$(Agent.OS)"
          path: 'e2e/node_modules'
          cacheHitVar: E2E_CACHE_RESTORED
        displayName: 'Cache npm packages for E2E project'

      - task: Cache@2
        inputs:
          key: 'migrator-v1 | "$(Agent.OS)" | e2e/Dockerfile-migrator'
          restoreKeys: |
            migrator-v1 | "$(Agent.OS)"
          path: '/tmp/.buildx-cache'
          cacheHitVar: MIGRATOR_CACHE_RESTORED
        displayName: 'Cache Docker for migrator'
