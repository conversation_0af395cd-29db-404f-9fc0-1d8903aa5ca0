

steps:
  - script: |
      # Configure AWS CLI with credentials
      aws configure set aws_access_key_id $(AWS_ACCESS_KEY_ID)
      aws configure set aws_secret_access_key $(AWS_SECRET_ACCESS_KEY)
      aws configure set region $(AWS_REGION)
      
      aws ecr get-login-password --region $(AWS_REGION) | docker login --username AWS --password-stdin $(ECR_URL)
    displayName: 'Login to Amazon ECR'
