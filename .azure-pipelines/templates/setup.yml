# MERGE
# SYSTEM_PULLREQUEST_SOURCECOMMITID #<------------
# SYSTEM_PULLREQUEST_SOURCEBRANCH=refs/heads/test-env
#  BUILD_BUILDID=93
#  BUILD_BUILDNUMBER=20240703.3
# BUILD_REASON=PullRequest
# BUILD_SOURCEBRANCH=refs/pull/66/merge
# BUILD_SOURCEBRANCHNAME=merge

# PUSH
# BUILD_REASON=IndividualCI
# BUILD_SOURCEBRANCH=refs/heads/release/ttt
# BUILD_SOURCEBRANCHNAME=ttt
# BUILD_SOURCEVERSION=46279d0def00d1680d7e9bcc62b651927479ffd6 #<------------
parameters:
  - name: repositoryToCheck
    type: string
    default: ''


steps:
  - template: aws-ecr-login.yml

  - script: |
      chmod -R +x .azure-pipelines/scripts
    displayName: 'Chmod scripts'

  - task: Bash@3
    inputs:
      filePath: '$(Build.SourcesDirectory)/.azure-pipelines/scripts/setup.sh'
    displayName: 'Set up build environment'

  - task: Bash@3
    inputs:
      filePath: '$(Build.SourcesDirectory)/.azure-pipelines/scripts/check.sh'
    displayName: 'Check if build should be canceled'
    condition: ne('${{ parameters.repositoryToCheck }}', '')
    env:
      ACCESS_TOKEN: $(AZURE_CANCEL_BUILD)
      REPOSITORY: ${{ parameters.repositoryToCheck }}
